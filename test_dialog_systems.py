#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Dialog Systems Module

This script tests the extracted dialog functions to ensure they work correctly
and maintain backward compatibility.
"""

import sys
import os

# Add the current directory to the path to import modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dialog_systems_import():
    """Test that the dialog systems module can be imported successfully."""
    try:
        from ui.dialog_systems import dialog_systems
        print("✅ Dialog systems module imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import dialog systems module: {e}")
        return False

def test_module_info():
    """Test the module info functionality."""
    try:
        from ui.dialog_systems import dialog_systems
        info = dialog_systems.get_module_info()
        
        print("📋 Module Information:")
        for key, value in info.items():
            print(f"   {key}: {value}")
        
        # Validate expected fields
        expected_fields = ['module_name', 'version', 'functions', 'status']
        for field in expected_fields:
            if field not in info:
                print(f"❌ Missing expected field: {field}")
                return False
        
        print("✅ Module info test passed")
        return True
    except Exception as e:
        print(f"❌ Module info test failed: {e}")
        return False

def test_legacy_wrappers():
    """Test that legacy wrapper functions work correctly."""
    try:
        # Test importing legacy wrappers from main file
        from a7_load_multilas_EEI_XCOR_PLOT_Final import get_analysis_type_and_parameters, show_next_action_dialog
        
        print("✅ Legacy wrapper functions imported successfully")
        print("   - get_analysis_type_and_parameters")
        print("   - show_next_action_dialog")
        
        return True
    except ImportError as e:
        print(f"❌ Failed to import legacy wrapper functions: {e}")
        return False

def test_function_availability():
    """Test that all expected functions are available."""
    try:
        from ui.dialog_systems import dialog_systems
        
        expected_functions = [
            'get_analysis_type_and_parameters',
            'show_next_action_dialog',
            'get_module_info'
        ]
        
        available_functions = [func for func in dir(dialog_systems) if not func.startswith('_')]
        
        print("📋 Available functions:")
        for func in available_functions:
            print(f"   - {func}")
        
        missing_functions = []
        for func in expected_functions:
            if not hasattr(dialog_systems, func):
                missing_functions.append(func)
        
        if missing_functions:
            print(f"❌ Missing expected functions: {missing_functions}")
            return False
        
        print("✅ All expected functions are available")
        return True
    except Exception as e:
        print(f"❌ Function availability test failed: {e}")
        return False

def test_state_management():
    """Test the state management functionality."""
    try:
        from ui.dialog_systems import dialog_systems
        
        # Test state initialization
        if not hasattr(dialog_systems, 'state'):
            print("❌ State management not initialized")
            return False
        
        if not hasattr(dialog_systems, 'last_selections'):
            print("❌ Last selections cache not initialized")
            return False
        
        print("✅ State management initialized correctly")
        return True
    except Exception as e:
        print(f"❌ State management test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("🧪 Running Dialog Systems Module Tests")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_dialog_systems_import),
        ("Module Info Test", test_module_info),
        ("Legacy Wrappers Test", test_legacy_wrappers),
        ("Function Availability Test", test_function_availability),
        ("State Management Test", test_state_management)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   Test failed!")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Dialog systems module is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
