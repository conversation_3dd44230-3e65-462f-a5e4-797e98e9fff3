# -*- coding: utf-8 -*-
"""
EEI Calculation Engine Module

This module contains pure calculation logic for EEI, CPEI, and PEIL optimization.
All functions are stateless and have no UI dependencies.

Author: Refactored from a7_load_multilas_EEI_XCOR_PLOT_Final.py
Created: 2024
Version: 1.0.0
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union
from eeimpcalc import eeimpcalc, calculate_cpei, calculate_peil
from eei_data_processing import EEIDataProcessor

# Configure module-specific logger
logger = logging.getLogger(__name__)

class EEICalculationEngine:
    """
    Pure calculation engine for EEI, CPEI, and PEIL optimization.

    This class contains only stateless calculation methods with no UI dependencies.
    All methods are static to emphasize the stateless nature.
    """

    @staticmethod
    def calculate_cpei_optimization(
        pvel: np.ndarray,
        svel: np.ndarray,
        rhob: np.ndarray,
        target: np.ndarray,
        n_range: Optional[np.ndarray] = None,
        phi_range: Optional[range] = None
    ) -> Dict[str, Union[np.ndarray, float, int]]:
        """
        Pure CPEI optimization calculation.

        Args:
            pvel: P-wave velocity array
            svel: S-wave velocity array
            rhob: Density array
            target: Target log array
            n_range: Range of n values to test (default: 0.1 to 2.0, step 0.1)
            phi_range: Range of phi values to test (default: -90 to 90 degrees)

        Returns:
            Dict containing:
                - correlation_matrix: 2D correlation matrix
                - n_values: Array of n values tested
                - phi_values: Array of phi values tested
                - optimal_params: Dict with optimal n, phi, and max correlation

        Raises:
            ValueError: If input arrays are invalid or incompatible
            RuntimeError: If optimization fails
        """
        try:
            # Input validation
            EEICalculationEngine._validate_optimization_inputs(
                pvel, svel, rhob, target, "CPEI"
            )

            # Set default ranges
            n_values = n_range if n_range is not None else np.arange(0.1, 2.1, 0.1)
            phi_values = phi_range if phi_range is not None else range(-90, 91)

            # Initialize correlation matrix
            correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)

            logger.info(f"Starting CPEI optimization: {len(n_values)} n values × {len(phi_values)} phi values")

            # Optimization loop
            for i, n in enumerate(n_values):
                for j, phi in enumerate(phi_values):
                    try:
                        cpei = calculate_cpei(pvel, svel, rhob, n, phi)
                        correlation = EEIDataProcessor.calculate_correlation_safe(cpei, target)
                        correlation_matrix[i, j] = correlation
                    except Exception as e:
                        logger.warning(f"CPEI calculation failed for n={n:.1f}, phi={phi}: {str(e)}")
                        correlation_matrix[i, j] = np.nan

            # Find optimal parameters
            optimal_params = EEICalculationEngine._find_optimal_2d(
                correlation_matrix, n_values, phi_values
            )

            logger.info(f"CPEI optimization complete. Optimal: n={optimal_params['n']:.1f}, phi={optimal_params['phi']}°")

            return {
                'correlation_matrix': correlation_matrix,
                'n_values': n_values,
                'phi_values': phi_values,
                'optimal_params': optimal_params
            }

        except Exception as e:
            logger.error(f"CPEI optimization failed: {str(e)}")
            raise RuntimeError(f"CPEI optimization failed: {str(e)}") from e

    @staticmethod
    def calculate_peil_optimization(
        pvel: np.ndarray,
        svel: np.ndarray,
        rhob: np.ndarray,
        target: np.ndarray,
        n_range: Optional[np.ndarray] = None,
        phi_range: Optional[range] = None
    ) -> Dict[str, Union[np.ndarray, float, int]]:
        """
        Pure PEIL optimization calculation.

        Identical structure to CPEI optimization but uses calculate_peil().

        Args:
            pvel: P-wave velocity array
            svel: S-wave velocity array
            rhob: Density array
            target: Target log array
            n_range: Range of n values to test
            phi_range: Range of phi values to test

        Returns:
            Dict containing optimization results

        Raises:
            ValueError: If input arrays are invalid
            RuntimeError: If optimization fails
        """
        try:
            # Input validation
            EEICalculationEngine._validate_optimization_inputs(
                pvel, svel, rhob, target, "PEIL"
            )

            # Set default ranges
            n_values = n_range if n_range is not None else np.arange(0.1, 2.1, 0.1)
            phi_values = phi_range if phi_range is not None else range(-90, 91)

            # Initialize correlation matrix
            correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)

            logger.info(f"Starting PEIL optimization: {len(n_values)} n values × {len(phi_values)} phi values")

            # Optimization loop
            for i, n in enumerate(n_values):
                for j, phi in enumerate(phi_values):
                    try:
                        peil = calculate_peil(pvel, svel, rhob, n, phi)
                        correlation = EEIDataProcessor.calculate_correlation_safe(peil, target)
                        correlation_matrix[i, j] = correlation
                    except Exception as e:
                        logger.warning(f"PEIL calculation failed for n={n:.1f}, phi={phi}: {str(e)}")
                        correlation_matrix[i, j] = np.nan

            # Find optimal parameters
            optimal_params = EEICalculationEngine._find_optimal_2d(
                correlation_matrix, n_values, phi_values
            )

            logger.info(f"PEIL optimization complete. Optimal: n={optimal_params['n']:.1f}, phi={optimal_params['phi']}°")

            return {
                'correlation_matrix': correlation_matrix,
                'n_values': n_values,
                'phi_values': phi_values,
                'optimal_params': optimal_params
            }

        except Exception as e:
            logger.error(f"PEIL optimization failed: {str(e)}")
            raise RuntimeError(f"PEIL optimization failed: {str(e)}") from e

    @staticmethod
    def calculate_eei_optimization(
        pvel: np.ndarray,
        svel: np.ndarray,
        rhob: np.ndarray,
        target: np.ndarray,
        calcmethod: int = 3,
        k_method: int = 1,
        k_value: Optional[float] = None,
        angle_range: Optional[range] = None
    ) -> Dict[str, Union[np.ndarray, float, int]]:
        """
        Pure EEI angle optimization calculation.

        Args:
            pvel: P-wave velocity array
            svel: S-wave velocity array
            rhob: Density array
            target: Target log array
            calcmethod: EEI calculation method (1, 2, or 3)
            k_method: K value determination method (1=calculate, 2=constant)
            k_value: Constant k value (if k_method=2)
            angle_range: Range of angles to test (default: -90 to 90 degrees)

        Returns:
            Dict containing:
                - correlations: Array of correlation values
                - angles: Array of angles tested
                - optimal_angle: Optimal angle in degrees
                - max_correlation: Maximum correlation achieved
                - k_used: K value used in calculations

        Raises:
            ValueError: If input parameters are invalid
            RuntimeError: If optimization fails
        """
        try:
            # Input validation
            EEICalculationEngine._validate_optimization_inputs(
                pvel, svel, rhob, target, "EEI"
            )

            # Determine k value
            k = EEICalculationEngine._determine_k_value(
                pvel, svel, k_method, k_value
            )

            # Set default angle range
            angles = angle_range if angle_range is not None else range(-90, 91)
            correlations = []

            logger.info(f"Starting EEI optimization: {len(angles)} angles, k={k:.4f}")

            # Optimization loop
            for angle in angles:
                try:
                    eei, _, _ = eeimpcalc(pvel, svel, rhob, angle, k, calcmethod=calcmethod)
                    correlation = EEIDataProcessor.calculate_correlation_safe(eei, target)
                    correlations.append(correlation)
                except Exception as e:
                    logger.warning(f"EEI calculation failed for angle {angle}: {str(e)}")
                    correlations.append(np.nan)

            # Find optimal angle
            correlations = np.array(correlations)
            optimal_result = EEICalculationEngine._find_optimal_1d(correlations, angles)

            logger.info(f"EEI optimization complete. Optimal angle: {optimal_result['optimal_value']}°")

            return {
                'correlations': correlations,
                'angles': list(angles),
                'optimal_angle': optimal_result['optimal_value'],
                'max_correlation': optimal_result['max_correlation'],
                'k_used': k
            }

        except Exception as e:
            logger.error(f"EEI optimization failed: {str(e)}")
            raise RuntimeError(f"EEI optimization failed: {str(e)}") from e

    # Private helper methods
    @staticmethod
    def _validate_optimization_inputs(
        pvel: np.ndarray,
        svel: np.ndarray,
        rhob: np.ndarray,
        target: np.ndarray,
        analysis_type: str
    ) -> None:
        """Validate inputs for optimization calculations."""
        if not all(isinstance(arr, np.ndarray) for arr in [pvel, svel, rhob, target]):
            raise ValueError("All input arrays must be numpy arrays")

        if not all(arr.size > 0 for arr in [pvel, svel, rhob, target]):
            raise ValueError("All input arrays must be non-empty")

        if not all(arr.shape == pvel.shape for arr in [svel, rhob, target]):
            raise ValueError("All input arrays must have the same shape")

        if not all(np.isfinite(arr).any() for arr in [pvel, svel, rhob, target]):
            raise ValueError(f"All input arrays must contain at least some finite values for {analysis_type}")

    @staticmethod
    def _find_optimal_2d(
        correlation_matrix: np.ndarray,
        n_values: np.ndarray,
        phi_values: range
    ) -> Dict[str, float]:
        """Find optimal parameters from 2D correlation matrix."""
        if np.all(np.isnan(correlation_matrix)):
            return {'n': np.nan, 'phi': np.nan, 'max_correlation': np.nan}

        max_idx = np.unravel_index(np.nanargmax(correlation_matrix), correlation_matrix.shape)

        return {
            'n': n_values[max_idx[0]],
            'phi': phi_values[max_idx[1]],
            'max_correlation': correlation_matrix[max_idx]
        }

    @staticmethod
    def _find_optimal_1d(
        correlations: np.ndarray,
        values: range
    ) -> Dict[str, float]:
        """Find optimal value from 1D correlation array."""
        if np.all(np.isnan(correlations)):
            return {'optimal_value': np.nan, 'max_correlation': np.nan}

        max_idx = np.nanargmax(correlations)

        return {
            'optimal_value': values[max_idx],
            'max_correlation': correlations[max_idx]
        }

    @staticmethod
    def _determine_k_value(
        pvel: np.ndarray,
        svel: np.ndarray,
        k_method: int,
        k_value: Optional[float]
    ) -> float:
        """Determine k value based on method."""
        if k_method == 1:
            # Calculate from velocity ratio
            velocity_ratio = svel / pvel
            k = np.nanmean(velocity_ratio**2)

            if not np.isfinite(k) or k <= 0:
                logger.warning(f"Invalid calculated k value: {k}, using default 0.25")
                k = 0.25

            return k
        else:
            # Use provided constant value
            if k_value is None or not np.isfinite(k_value) or k_value <= 0:
                logger.warning(f"Invalid provided k value: {k_value}, using default 0.25")
                return 0.25

            return k_value
