# -*- coding: utf-8 -*-
"""
Created on Thu Aug 29 11:15:20 2024

@author: devri.agustianto
"""

# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]

import numpy as np
import lasio
import tkinter as tk
from tkinter import filedialog, simpledialog, ttk, messagebox
import matplotlib.pyplot as plt
from eeimpcalc import eeimpcalc, calculate_cpei, calculate_peil  # Import the external module
import os
import pandas as pd
from tabulate import tabulate
from scipy import stats  # Import for linear regression
import re  # Import for regex pattern matching
import logging  # Import for comprehensive error logging

# Import new modular components
from eei_calculation_engine import EEICalculationEngine
from eei_data_processing import EEIDataProcessor
from eei_config import EEIConfig, LOG_KEYWORDS, ANALYSIS_PARAMS
from ui.helper_functions import safe_format_float, safe_format_parameter_string, validate_cpei_peil_inputs
from ui.file_management import (
    load_multiple_las_files, validate_essential_logs, generate_validation_report,
    categorize_log_curves, display_log_inventory, find_default_columns,
    analyze_log_availability, load_boundaries_from_excel, filter_excel_data_for_las_wells,
    load_excel_depth_ranges, log_available_curves
)
from ui.calculator_interface import calculator_interface
from ui.dialog_systems import dialog_systems

# Configure logging for debugging NoneType format string errors
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HELPER FUNCTIONS FOR SAFE STRING FORMATTING AND ERROR HANDLING
# (Functions moved to ui/helper_functions.py)

# Calculator functions moved to ui/calculator_interface.py

# Legacy function wrapper for backward compatibility
def validate_calculation_inputs(las_files, calculation_text):
    """Legacy wrapper for backward compatibility."""
    return calculator_interface.validate_calculation_inputs(las_files, calculation_text)

def handle_calculation_error(error_details, las_files):
    """Legacy wrapper for backward compatibility."""
    return calculator_interface.handle_calculation_error(error_details, las_files)



def execute_calculations_safely(las_files, calculations):
    """Legacy wrapper for backward compatibility."""
    return calculator_interface.execute_calculations_safely(las_files, calculations)

def handle_execution_error(las_files):
    """Legacy wrapper for backward compatibility."""
    return calculator_interface.handle_execution_error(las_files)

def show_calculator_interface(las_files):
    """Legacy wrapper for backward compatibility."""
    return calculator_interface.show_calculator_interface(las_files)

def get_calculations_for_eei(las_files):
    """Legacy wrapper for backward compatibility."""
    result = calculator_interface.get_calculations_for_eei(las_files)
    return result is not None  # Convert string result to boolean for backward compatibility

# COMPLATION OF FUNCTION
# Define keywords for broader log detection
log_keywords = {
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC'],  # P-wave slowness
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC'],  # S-wave slowness
    'PHIT': ['PHIT', 'PHID', 'PHI_D'],
    'PHIE': ['PHIE', 'PHIE_D'],
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY', 'RHOZ', 'RHO'],  # Added more density log keywords
    'SWT': ['SWT', 'SW', 'WATER_SAT'],
    'SWE': ['SWE', 'SWE_D'],
    'DEPTH': ['DEPTH', 'MD', 'MEASURED_DEPTH'],
    'P-WAVE': ['P-WAVE', 'P_VELOCITY', 'VP'],
    'S-WAVE': ['S-WAVE', 'S_VELOCITY', 'VS'],
    'FLUID_CODE': ['FLUID_CODE', 'FLUID'],
    'FLUID_PETREL': ['FLUID_PETREL'],
    'LITHO_CODE': ['LITHO_CODE', 'LITHOLOGY','LITHO_PETREL'],
    'GR': ['GR', 'GAMMA_RAY', 'GR_LOG'],
    'NPHI': ['NPHI', 'NEUTRON', 'NEUTRON_POROSITY'],
    'KSOLID': ['KSOLID', 'K_SOLID'],
    'GSOLID': ['GSOLID', 'G_SOLID'],
    'KSAT': ['KSAT', 'K_SATURATION'],
    'GSAT': ['GSAT', 'G_SATURATION'],
    'KDRY': ['KDRY', 'K_DRY'],
    'GDRY': ['GDRY', 'G_DRY'],
    'VCL': ['VCL', 'VOL_WETCLAY', 'V_CLAY'],
    'RT': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD', 'AT90']
}

# load_multiple_las_files function moved to ui/file_management.py

# validate_essential_logs function moved to ui/file_management.py

# generate_validation_report function moved to ui/file_management.py

# categorize_log_curves function moved to ui/file_management.py

# display_log_inventory function moved to ui/file_management.py

# log_available_curves function moved to ui/file_management.py

# find_default_columns function moved to ui/file_management.py


def nanaware_corrcoef(x, y):
    """
    Backward compatibility wrapper for correlation calculation.
    Delegates to the modular EEIDataProcessor.calculate_correlation_safe().
    """
    return EEIDataProcessor.calculate_correlation_safe(x, y)

def find_nearest_index(array, value):
    """
    Backward compatibility wrapper for nearest index finding.
    Delegates to the modular EEIDataProcessor.find_nearest_index().
    """
    return EEIDataProcessor.find_nearest_index(array, value)

def merge_well_data(las_files, columns, target_log, depth_ranges):
    """
    Merge data from multiple wells into a single array.
    Includes robust validation and logging.
    """
    merged_depth, merged_dt, merged_dts, merged_rhob, merged_target = [], [], [], [], []

    for las in las_files:
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges.get(well_name, (None, None))

        if top_depth is None or bottom_depth is None:
            logger.warning(f"Well {well_name}: Skipping merge due to missing depth range.")
            continue

        # Extract data with existence checks
        curve_keys = {key.upper() for key in las.curves.keys()} # Use uppercase for comparison

        depth_key_generic = 'DEPTH'
        dt_key_generic = 'DT'
        dts_key_generic = 'DTS'
        rhob_key_generic = 'RHOB'

        depth_key_actual = columns.get(depth_key_generic)
        dt_key_actual = columns.get(dt_key_generic)
        dts_key_actual = columns.get(dts_key_generic)
        rhob_key_actual = columns.get(rhob_key_generic)

        # Target log can be a generic keyword or an actual mnemonic
        target_key_actual = columns.get(target_log.upper()) or target_log # Check columns first, then direct

        # Validate essential log keys from `columns` dictionary
        if not depth_key_actual or depth_key_actual.upper() not in curve_keys:
            logger.warning(f"Well {well_name}: Skipping merge due to missing or unmapped DEPTH log ('{depth_key_actual}').")
            continue
        if not dt_key_actual or dt_key_actual.upper() not in curve_keys or \
           not dts_key_actual or dts_key_actual.upper() not in curve_keys or \
           not rhob_key_actual or rhob_key_actual.upper() not in curve_keys:
            logger.warning(f"Well {well_name}: Skipping merge due to missing or unmapped essential logs (DT: '{dt_key_actual}', DTS: '{dts_key_actual}', or RHOB: '{rhob_key_actual}').")
            continue

        # Validate target log key
        if not target_key_actual or target_key_actual.upper() not in curve_keys:
            # If target_log itself (original case) is in curves, use it
            if target_log in las.curves.keys():
                target_key_actual = target_log
                logger.info(f"Well {well_name}: Using direct mnemonic '{target_log}' for target log as mapped key '{columns.get(target_log.upper())}' not found.")
            else:
                logger.warning(f"Well {well_name}: Skipping merge due to missing or unmapped target log '{target_log}' (tried '{target_key_actual}').")
                continue

        depth = np.array(las[depth_key_actual].data)
        dt = np.array(las[dt_key_actual].data)
        dts = np.array(las[dts_key_actual].data)
        rhob = np.array(las[rhob_key_actual].data)
        target = np.array(las[target_key_actual].data)

        # Validate array lengths and content
        if not all(hasattr(arr, 'size') and arr.size > 0 for arr in [depth, dt, dts, rhob, target]):
            logger.warning(f"Well {well_name}: Skipping merge due to empty data arrays for one or more required logs.")
            continue

        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)

        if top_index is None or bottom_index is None : # find_nearest_index could return None if array is empty, though checked above
             logger.warning(f"Well {well_name}: Skipping merge due to invalid top/bottom indices (possibly empty depth array after initial check).")
             continue

        if top_index > bottom_index: # Ensure correct order
            logger.info(f"Well {well_name}: Top depth index ({top_index} for {top_depth}) is after bottom depth index ({bottom_index} for {bottom_depth}). Swapping them.")
            top_index, bottom_index = bottom_index, top_index

        if top_index == bottom_index and depth[top_index:bottom_index+1].size == 0 : # Handles single point interval correctly
             logger.warning(f"Well {well_name}: Skipping merge as depth interval resulted in zero data points ({top_depth} to {bottom_depth}).")
             continue


        # Extend merged arrays
        merged_depth.extend(depth[top_index:bottom_index+1])
        merged_dt.extend(dt[top_index:bottom_index+1])
        merged_dts.extend(dts[top_index:bottom_index+1])
        merged_rhob.extend(rhob[top_index:bottom_index+1])
        merged_target.extend(target[top_index:bottom_index+1])
        logger.info(f"Well {well_name}: Successfully merged data for depth range {top_depth}-{bottom_depth} (indices {top_index}-{bottom_index}). Added {len(depth[top_index:bottom_index+1])} data points.")


    # Convert to numpy arrays and check for emptiness
    if not merged_depth:
        logger.error("Merged data is empty. No valid data available for analysis from any well.")
        return None, None, None, None, None

    logger.info(f"Successfully merged data from contributing wells. Total merged data points: {len(merged_depth)}.")
    return (np.array(merged_depth), np.array(merged_dt), np.array(merged_dts),
            np.array(merged_rhob), np.array(merged_target))

# get_analysis_type_and_parameters function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def get_analysis_type_and_parameters():
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_analysis_type_and_parameters()

def calculate_eei_optimum_angle(las, actual_base_log_mnemonics, target_log_actual_mnemonic, top_depth, bottom_depth, calcmethod, k_method=1, k_value=None):
    """
    UI wrapper for EEI optimization - preserves all existing UI functionality.

    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """

    # Extract and prepare data (existing code preserved)
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find depth indices (existing code preserved)
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice arrays (existing code preserved)
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    try:
        # Convert to velocities (existing code preserved)
        pvel = 304800 / dt
        svel = 304800 / dts

        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_eei_optimization(
            pvel, svel, rhob, target, calcmethod, k_method, k_value
        )

        # Extract results for UI display (preserving existing format)
        optimum_angle = results['optimal_angle']
        max_correlation = results['max_correlation']
        angles = results['angles']
        correlations = results['correlations']
        k_used = results['k_used']

        # ALL existing result processing and UI updates remain identical
        logger.info(f"Optimization completed. Optimum angle: {optimum_angle}°, Max correlation: {safe_format_float(max_correlation, precision=4)}")
        logger.info(f"K value used: {safe_format_float(k_used, precision=4)}")

        return optimum_angle, max_correlation, angles, correlations.tolist()

    except Exception as e:
        logger.error(f"Error in calculate_eei_optimum_angle: {str(e)}")
        return None, None, range(-90, 91), [np.nan] * 181

def calculate_eei_optimum_angle_merged(depth, dt, dts, rhob, target, calcmethod, k_method=1, k_value=None):
    """
    UI wrapper for EEI optimization on merged data - preserves all existing UI functionality.

    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """
    try:
        # Convert to velocities (existing code preserved)
        pvel = 304800 / dt
        svel = 304800 / dts

        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_eei_optimization(
            pvel, svel, rhob, target, calcmethod, k_method, k_value
        )

        # Extract results for UI display (preserving existing format)
        optimum_angle = results['optimal_angle']
        max_correlation = results['max_correlation']
        angles = results['angles']
        correlations = results['correlations']
        k_used = results['k_used']

        # ALL existing result processing and UI updates remain identical
        logger.info(f"Optimization completed. Optimum angle: {optimum_angle}°, Max correlation: {safe_format_float(max_correlation, precision=4)}")
        logger.info(f"K value used (merged): {safe_format_float(k_used, precision=4)}")

        return optimum_angle, max_correlation, angles, correlations.tolist()

    except Exception as e:
        logger.error(f"Error in calculate_eei_optimum_angle_merged: {str(e)}")
        return None, None, range(-90, 91), [np.nan] * 181

def calculate_cpei_optimum_parameters(las, actual_base_log_mnemonics, target_log_actual_mnemonic, top_depth, bottom_depth):
    """
    UI wrapper for CPEI optimization - preserves all existing UI functionality.

    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """

    # ALL existing UI code remains identical:
    # - Progress dialogs
    # - Error handling
    # - User feedback
    # - Data extraction and validation

    print(f"Starting CPEI optimization for target log: {target_log_actual_mnemonic}")

    # Extract and prepare data (existing code preserved)
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find depth indices (existing code preserved)
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice arrays (existing code preserved)
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    # Convert to velocities (existing code preserved)
    pvel = 304800 / dt
    svel = 304800 / dts

    try:
        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_cpei_optimization(
            pvel, svel, rhob, target
        )

        # Extract results for UI display (preserving existing format)
        optimal_n = results['optimal_params']['n']
        optimal_phi = results['optimal_params']['phi']
        max_correlation = results['optimal_params']['max_correlation']
        n_values = results['n_values']
        phi_values = results['phi_values']
        correlation_matrix = results['correlation_matrix']

        # ALL existing result processing and UI updates remain identical
        print(f"CPEI optimization complete:")
        print(f"  Optimal n: {safe_format_float(optimal_n, precision=1, default='N/A')}")
        print(f"  Optimal phi: {optimal_phi}°")
        print(f"  Maximum correlation: {safe_format_float(max_correlation, precision=4, default='N/A')}")

        return optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix

    except Exception as e:
        # Enhanced error handling with backend error context
        logger.error(f"CPEI optimization failed: {str(e)}")
        print(f"Error in CPEI optimization: {str(e)}")
        return None, None, None, np.arange(0.1, 2.1, 0.1), range(-90, 91), np.full((20, 181), np.nan)

def calculate_peil_optimum_parameters(las, actual_base_log_mnemonics, target_log_actual_mnemonic, top_depth, bottom_depth):
    """
    UI wrapper for PEIL optimization - preserves all existing UI functionality.

    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """

    print(f"Starting PEIL optimization for target log: {target_log_actual_mnemonic}")

    # Extract and prepare data (existing code preserved)
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find depth indices (existing code preserved)
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice arrays (existing code preserved)
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    # Convert to velocities (existing code preserved)
    pvel = 304800 / dt
    svel = 304800 / dts

    try:
        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_peil_optimization(
            pvel, svel, rhob, target
        )

        # Extract results for UI display (preserving existing format)
        optimal_n = results['optimal_params']['n']
        optimal_phi = results['optimal_params']['phi']
        max_correlation = results['optimal_params']['max_correlation']
        n_values = results['n_values']
        phi_values = results['phi_values']
        correlation_matrix = results['correlation_matrix']

        # ALL existing result processing and UI updates remain identical
        print(f"PEIL optimization complete:")
        print(f"  Optimal n: {safe_format_float(optimal_n, precision=1, default='N/A')}")
        print(f"  Optimal phi: {optimal_phi}°")
        print(f"  Maximum correlation: {safe_format_float(max_correlation, precision=4, default='N/A')}")

        return optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix

    except Exception as e:
        # Enhanced error handling with backend error context
        logger.error(f"PEIL optimization failed: {str(e)}")
        print(f"Error in PEIL optimization: {str(e)}")
        return None, None, None, np.arange(0.1, 2.1, 0.1), range(-90, 91), np.full((20, 181), np.nan)

def calculate_eei(las, actual_base_log_mnemonics, target_log_actual_mnemonic, vcl_actual_mnemonic, top_depth, bottom_depth, angle, calcmethod, k_method=1, k_value=None):
    """
    Calculate EEI using the given angle and clip results based on percentiles.
    Uses actual mnemonics specific to the 'las' file.
    actual_base_log_mnemonics is a dict: {'DEPTH': 'actual_depth_name', 'DT': 'actual_dt_name', ...}
    target_log_actual_mnemonic is the string name of the target curve.
    vcl_actual_mnemonic is the string name of the VCL curve, or None.
    """
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find nearest indices for top and bottom depths
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    # Ensure top_index is smaller than bottom_index
    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice the arrays using the indices
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    # Convert slowness to velocity (assuming microseconds/ft to m/s conversion)
    pvel = 304800 / dt  # Convert microseconds/ft to m/s
    svel = 304800 / dts  # Convert microseconds/ft to m/s


    # Determine k value based on method selected
    if k_method == 1:
        # Calculate k from velocity ratio
        velocity_ratio = svel / pvel
        k = np.nanmean(velocity_ratio**2)

        # Validate calculated k value
        if k is None or not np.isfinite(k) or k <= 0:
            logger.warning(f"Invalid calculated k value: {k}, using default k=0.25")
            k = 0.25  # Default reasonable k value

        logger.debug(f"Calculated k value from logs (final EEI): {safe_format_float(k, precision=4)}")
    else:
        k = k_value

        # Validate provided k value
        if k is None or not np.isfinite(k) or k <= 0:
            logger.warning(f"Invalid provided k value: {k}, using default k=0.25")
            k = 0.25  # Default reasonable k value

        logger.debug(f"Using constant k value (final EEI): {safe_format_float(k, precision=4)}")

    # Calculate EEI with error handling
    try:
        eei, _, _ = eeimpcalc(pvel, svel, rhob, angle, k, calcmethod=calcmethod)

        # Validate EEI calculation result
        if eei is None or not hasattr(eei, '__len__'):
            logger.error("EEI calculation returned invalid result")
            return None

    except Exception as e:
        logger.error(f"Error calculating EEI: {str(e)}")
        return None

     # Step 1: Min-Max Normalization of EEI
    eei_min = np.nanmin(eei)
    eei_max = np.nanmax(eei)

    # Avoid division by zero if min and max are the same
    if eei_max != eei_min:
        norm_eei = (eei - eei_min) / (eei_max - eei_min)
    else:
        norm_eei = eei  # No scaling needed if all values are the same

    # Step 2: Scale the normalized EEI to match the target log range
    target_min = np.nanmin(target)
    target_max = np.nanmax(target)

    normalized_eei = norm_eei * (target_max - target_min) + target_min

    # Get VOL_WETCLAY data
    vol_wetclay = None
    if vcl_actual_mnemonic and vcl_actual_mnemonic in las.curves:
        vol_wetclay = np.array(las[vcl_actual_mnemonic].data)[top_index:bottom_index+1]

    # Warning for missing VCL is better handled where find_default_columns is called.
    # if vol_wetclay is None:
    #     print(f"Info: VOL_WETCLAY (mnemonic: {vcl_actual_mnemonic}) not available or not found for well {las.well.WELL.value} in calculate_eei. Proceeding without VOL_WETCLAY data.")

    # Remove NaN and Inf values, and ensure consistent lengths
    mask = np.isfinite(depth) & np.isfinite(target) & np.isfinite(normalized_eei)
    if vol_wetclay is not None:
        mask = mask & np.isfinite(vol_wetclay)

    depth = depth[mask]
    target = target[mask]
    normalized_eei = normalized_eei[mask]
    if vol_wetclay is not None:
        vol_wetclay = vol_wetclay[mask]

    # Check if arrays are empty or contain only NaNs
    if depth.size == 0 or target.size == 0 or normalized_eei.size == 0:
        print(f"No valid data to process for well {las.well.WELL.value}. Skipping EEI calculation.")
        return None, None, None, None

    # Use np.nanpercentile to compute percentiles while ignoring NaNs
    eei_low, eei_high = np.nanpercentile(normalized_eei, [2, 98])
    target_low, target_high = np.nanpercentile(target, [2, 98])

    # Clip normalized_eei and target based on 2nd and 98th percentiles
    normalized_eei = np.clip(normalized_eei, eei_low, eei_high)
    target = np.clip(target, target_low, target_high)

    return depth, target, normalized_eei, vol_wetclay

def calculate_cpei_for_plotting(las, actual_base_log_mnemonics, target_log_actual_mnemonic, vcl_actual_mnemonic, top_depth, bottom_depth, optimal_n, optimal_phi):
    """
    Calculate CPEI using the optimal parameters and prepare data for plotting.
    Similar to calculate_eei but uses calculate_cpei instead of eeimpcalc.
    """
    try:
        depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
        dt = np.array(las[actual_base_log_mnemonics['DT']].data)
        dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
        rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
        target = np.array(las[target_log_actual_mnemonic].data)

        # Handle VCL (optional)
        vol_wetclay = None
        if vcl_actual_mnemonic and vcl_actual_mnemonic in las.curves:
            vol_wetclay = np.array(las[vcl_actual_mnemonic].data)

        # Convert slowness to velocity
        pvel = 304800 / dt  # Convert microseconds/ft to m/s
        svel = 304800 / dts  # Convert microseconds/ft to m/s

        # Validate input arrays
        if not all(np.isfinite(arr).any() for arr in [pvel, svel, rhob, target]):
            logger.error("One or more input arrays contain no finite values for CPEI plotting")
            return None, None, None, None

        # Calculate CPEI with error handling
        cpei = calculate_cpei(pvel, svel, rhob, optimal_n, optimal_phi)

        if cpei is None or not hasattr(cpei, '__len__'):
            logger.error("CPEI calculation returned invalid result for plotting")
            return None, None, None, None

        # Min-Max Normalization of CPEI
        cpei_min = np.nanmin(cpei)
        cpei_max = np.nanmax(cpei)

        if cpei_max != cpei_min:
            norm_cpei = (cpei - cpei_min) / (cpei_max - cpei_min)
        else:
            norm_cpei = cpei

        # Find nearest indices for depth range
        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)

        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index

        # Slice arrays to depth range
        depth = depth[top_index:bottom_index+1]
        target = target[top_index:bottom_index+1]
        normalized_cpei = norm_cpei[top_index:bottom_index+1]
        if vol_wetclay is not None:
            vol_wetclay = vol_wetclay[top_index:bottom_index+1]

        # Check if arrays are empty
        if depth.size == 0 or target.size == 0 or normalized_cpei.size == 0:
            print(f"No valid data to process for CPEI plotting in well {las.well.WELL.value}.")
            return None, None, None, None

        # Clip based on percentiles
        cpei_low, cpei_high = np.nanpercentile(normalized_cpei, [2, 98])
        target_low, target_high = np.nanpercentile(target, [2, 98])

        normalized_cpei = np.clip(normalized_cpei, cpei_low, cpei_high)
        target = np.clip(target, target_low, target_high)

        return depth, target, normalized_cpei, vol_wetclay

    except Exception as e:
        logger.error(f"Error in calculate_cpei_for_plotting: {str(e)}")
        return None, None, None, None

def calculate_peil_for_plotting(las, actual_base_log_mnemonics, target_log_actual_mnemonic, vcl_actual_mnemonic, top_depth, bottom_depth, optimal_n, optimal_phi):
    """
    Calculate PEIL using the optimal parameters and prepare data for plotting.
    Similar to calculate_eei but uses calculate_peil instead of eeimpcalc.
    """
    try:
        depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
        dt = np.array(las[actual_base_log_mnemonics['DT']].data)
        dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
        rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
        target = np.array(las[target_log_actual_mnemonic].data)

        # Handle VCL (optional)
        vol_wetclay = None
        if vcl_actual_mnemonic and vcl_actual_mnemonic in las.curves:
            vol_wetclay = np.array(las[vcl_actual_mnemonic].data)

        # Convert slowness to velocity
        pvel = 304800 / dt  # Convert microseconds/ft to m/s
        svel = 304800 / dts  # Convert microseconds/ft to m/s

        # Validate input arrays
        if not all(np.isfinite(arr).any() for arr in [pvel, svel, rhob, target]):
            logger.error("One or more input arrays contain no finite values for PEIL plotting")
            return None, None, None, None

        # Calculate PEIL with error handling
        peil = calculate_peil(pvel, svel, rhob, optimal_n, optimal_phi)

        if peil is None or not hasattr(peil, '__len__'):
            logger.error("PEIL calculation returned invalid result for plotting")
            return None, None, None, None

        # Min-Max Normalization of PEIL
        peil_min = np.nanmin(peil)
        peil_max = np.nanmax(peil)

        if peil_max != peil_min:
            norm_peil = (peil - peil_min) / (peil_max - peil_min)
        else:
            norm_peil = peil

        # Find nearest indices for depth range
        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)

        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index

        # Slice arrays to depth range
        depth = depth[top_index:bottom_index+1]
        target = target[top_index:bottom_index+1]
        normalized_peil = norm_peil[top_index:bottom_index+1]
        if vol_wetclay is not None:
            vol_wetclay = vol_wetclay[top_index:bottom_index+1]

        # Check if arrays are empty
        if depth.size == 0 or target.size == 0 or normalized_peil.size == 0:
            print(f"No valid data to process for PEIL plotting in well {las.well.WELL.value}.")
            return None, None, None, None

        # Clip based on percentiles
        peil_low, peil_high = np.nanpercentile(normalized_peil, [2, 98])
        target_low, target_high = np.nanpercentile(target, [2, 98])

        normalized_peil = np.clip(normalized_peil, peil_low, peil_high)
        target = np.clip(target, target_low, target_high)

        return depth, target, normalized_peil, vol_wetclay

    except Exception as e:
        logger.error(f"Error in calculate_peil_for_plotting: {str(e)}")
        return None, None, None, None

def calculate_optimal_crossplot_limits(x_data, y_data, correlation=None, global_x_min=None, global_x_max=None, global_y_min=None, global_y_max=None, well_name="Unknown"):
    """
    Calculate optimal axis limits for crossplot visualization based on data distribution analysis.
    Uses global percentiles as fallback when individual well data is insufficient or invalid.

    Args:
        x_data: Array of x-axis data (calculated curve: EEI/CPEI/PEIL)
        y_data: Array of y-axis data (target log)
        correlation: Correlation coefficient (optional, for scaling optimization)
        global_x_min: Global minimum for x-axis (fallback)
        global_x_max: Global maximum for x-axis (fallback)
        global_y_min: Global minimum for y-axis (fallback)
        global_y_max: Global maximum for y-axis (fallback)
        well_name: Name of the well (for logging)

    Returns:
        tuple: (x_min, x_max, y_min, y_max) optimized axis limits
    """
    try:
        # Remove NaN values for analysis
        valid_mask = ~(np.isnan(x_data) | np.isnan(y_data))
        x_clean = x_data[valid_mask]
        y_clean = y_data[valid_mask]

        if len(x_clean) < 2:
            # Use global percentiles as fallback if insufficient data
            logger.warning(f"Insufficient valid data for crossplot limits in well {well_name} "
                          f"({len(x_clean)} valid points). Using global percentiles as fallback.")

            # Use global percentiles if available, otherwise use simple range
            if (global_x_min is not None and global_x_max is not None and
                global_y_min is not None and global_y_max is not None):
                return global_x_min, global_x_max, global_y_min, global_y_max
            else:
                # Last resort: try simple range with robust NaN handling
                try:
                    x_min_fallback = np.nanmin(x_data) if np.isfinite(np.nanmin(x_data)) else 0.0
                    x_max_fallback = np.nanmax(x_data) if np.isfinite(np.nanmax(x_data)) else 1.0
                    y_min_fallback = np.nanmin(y_data) if np.isfinite(np.nanmin(y_data)) else 0.0
                    y_max_fallback = np.nanmax(y_data) if np.isfinite(np.nanmax(y_data)) else 1.0

                    # Ensure min < max
                    if x_min_fallback >= x_max_fallback:
                        x_min_fallback, x_max_fallback = 0.0, 1.0
                    if y_min_fallback >= y_max_fallback:
                        y_min_fallback, y_max_fallback = 0.0, 1.0

                    return x_min_fallback, x_max_fallback, y_min_fallback, y_max_fallback
                except:
                    logger.error(f"All fallback methods failed for well {well_name}. Using default limits (0, 1).")
                    return 0.0, 1.0, 0.0, 1.0

        # Calculate basic statistics for both axes
        x_stats = {
            'mean': np.mean(x_clean),
            'std': np.std(x_clean),
            'median': np.median(x_clean),
            'q25': np.percentile(x_clean, 25),
            'q75': np.percentile(x_clean, 75),
            'iqr': np.percentile(x_clean, 75) - np.percentile(x_clean, 25)
        }

        y_stats = {
            'mean': np.mean(y_clean),
            'std': np.std(y_clean),
            'median': np.median(y_clean),
            'q25': np.percentile(y_clean, 25),
            'q75': np.percentile(y_clean, 75),
            'iqr': np.percentile(y_clean, 75) - np.percentile(y_clean, 25)
        }

        # Detect outliers using IQR method
        x_outlier_threshold = 1.5 * x_stats['iqr']
        y_outlier_threshold = 1.5 * y_stats['iqr']

        x_lower_fence = x_stats['q25'] - x_outlier_threshold
        x_upper_fence = x_stats['q75'] + x_outlier_threshold
        y_lower_fence = y_stats['q25'] - y_outlier_threshold
        y_upper_fence = y_stats['q75'] + y_outlier_threshold

        # Calculate data density in different regions
        x_core_mask = (x_clean >= x_lower_fence) & (x_clean <= x_upper_fence)
        y_core_mask = (y_clean >= y_lower_fence) & (y_clean <= y_upper_fence)
        core_data_ratio = np.sum(x_core_mask & y_core_mask) / len(x_clean)

        # Adaptive percentile selection based on data characteristics
        if core_data_ratio > 0.85:
            # Most data is well-behaved, use tighter bounds
            x_percentiles = [5, 95]
            y_percentiles = [5, 95]
        elif core_data_ratio > 0.70:
            # Some outliers present, use moderate bounds
            x_percentiles = [2, 98]
            y_percentiles = [2, 98]
        else:
            # Many outliers, use wider bounds to capture more data
            x_percentiles = [1, 99]
            y_percentiles = [1, 99]

        # Adjust percentiles based on correlation strength
        if correlation is not None:
            abs_corr = abs(correlation)
            if abs_corr > 0.7:
                # Strong correlation: tighten bounds to focus on main trend
                x_percentiles = [max(x_percentiles[0], 3), min(x_percentiles[1], 97)]
                y_percentiles = [max(y_percentiles[0], 3), min(y_percentiles[1], 97)]
            elif abs_corr < 0.3:
                # Weak correlation: expand bounds to show scatter pattern
                x_percentiles = [max(x_percentiles[0] - 1, 0.5), min(x_percentiles[1] + 1, 99.5)]
                y_percentiles = [max(y_percentiles[0] - 1, 0.5), min(y_percentiles[1] + 1, 99.5)]

        # Calculate final limits
        x_min = np.percentile(x_clean, x_percentiles[0])
        x_max = np.percentile(x_clean, x_percentiles[1])
        y_min = np.percentile(y_clean, y_percentiles[0])
        y_max = np.percentile(y_clean, y_percentiles[1])

        # Add small buffer to prevent data points from touching axes
        x_range = x_max - x_min
        y_range = y_max - y_min

        buffer_factor = 0.02  # 2% buffer
        x_buffer = x_range * buffer_factor
        y_buffer = y_range * buffer_factor

        x_min -= x_buffer
        x_max += x_buffer
        y_min -= y_buffer
        y_max += y_buffer

        # Ensure minimum range to avoid degenerate plots
        min_range_factor = 0.01
        if x_range < min_range_factor * abs(x_stats['mean']):
            center = (x_min + x_max) / 2
            half_range = min_range_factor * abs(x_stats['mean']) / 2
            x_min = center - half_range
            x_max = center + half_range

        if y_range < min_range_factor * abs(y_stats['mean']):
            center = (y_min + y_max) / 2
            half_range = min_range_factor * abs(y_stats['mean']) / 2
            y_min = center - half_range
            y_max = center + half_range

        return x_min, x_max, y_min, y_max

    except Exception as e:
        logger.warning(f"Error in calculate_optimal_crossplot_limits for well {well_name}: {str(e)}, using fallback methods")

        # Try global percentiles first
        if (global_x_min is not None and global_x_max is not None and
            global_y_min is not None and global_y_max is not None):
            logger.info(f"Using global percentiles as fallback for crossplot limits in well {well_name}")
            return global_x_min, global_x_max, global_y_min, global_y_max

        # Try simple percentile method with robust error handling
        try:
            x_min = np.nanpercentile(x_data, 2)
            x_max = np.nanpercentile(x_data, 98)
            y_min = np.nanpercentile(y_data, 2)
            y_max = np.nanpercentile(y_data, 98)

            # Validate that percentiles are finite
            if not all(np.isfinite([x_min, x_max, y_min, y_max])):
                raise ValueError("Percentiles are not finite")

            # Ensure min < max
            if x_min >= x_max:
                x_min, x_max = 0.0, 1.0
            if y_min >= y_max:
                y_min, y_max = 0.0, 1.0

            return x_min, x_max, y_min, y_max

        except Exception as e2:
            logger.error(f"All fallback methods failed for crossplot limits in well {well_name}: {str(e2)}. Using default limits (0, 1).")
            return 0.0, 1.0, 0.0, 1.0

def calculate_global_percentiles_for_axis_limits(all_wells_data):
    """
    Calculate global 2nd and 98th percentiles from all wells' target and normalized_eei data.

    This function combines data from all wells to provide robust axis scaling that works
    consistently across all wells, even when individual wells have problematic data.

    Args:
        all_wells_data: List of dictionaries containing well data with keys:
                       'target', 'normalized_eei', 'well_name', etc.

    Returns:
        tuple: (global_min, global_max) - 2nd and 98th percentiles from combined data
               Returns (0, 1) as fallback if no valid data is found
    """
    try:
        all_valid_data = []
        wells_with_data = 0

        # Collect all valid data from all wells
        for well_data in all_wells_data:
            target = well_data.get('target')
            normalized_eei = well_data.get('normalized_eei')
            well_name = well_data.get('well_name', 'Unknown')

            # Skip wells with missing data
            if target is None or normalized_eei is None:
                logger.debug(f"Skipping well {well_name} - missing target or normalized_eei data")
                continue

            # Convert to numpy arrays if needed
            target = np.asarray(target)
            normalized_eei = np.asarray(normalized_eei)

            # Create finite value mask for both arrays
            target_finite_mask = np.isfinite(target)
            eei_finite_mask = np.isfinite(normalized_eei)

            # Extract finite values
            target_finite = target[target_finite_mask]
            eei_finite = normalized_eei[eei_finite_mask]

            # Add to global collection if we have valid data
            if len(target_finite) > 0:
                all_valid_data.extend(target_finite)
                wells_with_data += 1
                logger.debug(f"Added {len(target_finite)} target values from well {well_name}")

            if len(eei_finite) > 0:
                all_valid_data.extend(eei_finite)
                logger.debug(f"Added {len(eei_finite)} normalized_eei values from well {well_name}")

        # Check if we have sufficient data
        if len(all_valid_data) < 2:
            logger.warning(f"Insufficient valid data for global percentile calculation. "
                          f"Found {len(all_valid_data)} valid values from {wells_with_data} wells. "
                          f"Using fallback limits (0, 1).")
            return 0.0, 1.0

        # Convert to numpy array for percentile calculation
        all_valid_data = np.array(all_valid_data)

        # Calculate global percentiles using nanpercentile for extra safety
        global_min = np.nanpercentile(all_valid_data, 2)
        global_max = np.nanpercentile(all_valid_data, 98)

        # Validate that percentiles are finite
        if not (np.isfinite(global_min) and np.isfinite(global_max)):
            logger.warning(f"Global percentiles are not finite: min={global_min}, max={global_max}. "
                          f"Using fallback limits (0, 1).")
            return 0.0, 1.0

        # Ensure min < max
        if global_min >= global_max:
            logger.warning(f"Global min ({global_min}) >= max ({global_max}). "
                          f"Adding small buffer to max value.")
            global_max = global_min + 1.0

        logger.info(f"Calculated global percentiles from {len(all_valid_data)} values "
                   f"across {wells_with_data} wells: min={global_min:.4f}, max={global_max:.4f}")

        return global_min, global_max

    except Exception as e:
        logger.error(f"Error calculating global percentiles: {str(e)}. Using fallback limits (0, 1).")
        return 0.0, 1.0

def plot_eei_vs_target(all_wells_data, target_log, depth_ranges):
    """
    Create separate three-column plots for each well:
    1. Depth vs VOL_WETCLAY (if available)
    2. Depth vs Target Log and calculated curve (EEI/CPEI/PEIL with optimum parameters)
    3. Crossplot of calculated curve vs Target Log

    Automatically detects analysis type from the angle parameter format.
    Uses global percentile statistics for robust axis scaling across all wells.
    """
    # Calculate global percentiles from all wells for robust axis scaling
    global_min, global_max = calculate_global_percentiles_for_axis_limits(all_wells_data)
    logger.info(f"Using global percentiles for axis scaling: min={global_min:.4f}, max={global_max:.4f}")

    # Ask user for x-axis range for target log and EEI in the second column
    root = tk.Tk()
    root.withdraw()

    # Create a custom dialog that allows empty inputs
    class CustomRangeDialog(tk.simpledialog.Dialog):
        def __init__(self, parent, title, target_log):
            self.target_log = target_log
            self.x_min = None
            self.x_max = None
            super().__init__(parent, title)

        def body(self, master):
            ttk.Label(master, text=f"Enter x-axis range for {self.target_log} and EEI:").grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 10))
            ttk.Label(master, text="Minimum value (leave empty for auto):").grid(row=1, column=0, sticky="w", pady=5)
            ttk.Label(master, text="Maximum value (leave empty for auto):").grid(row=2, column=0, sticky="w", pady=5)

            self.min_entry = ttk.Entry(master)
            self.min_entry.grid(row=1, column=1, padx=5, pady=5)

            self.max_entry = ttk.Entry(master)
            self.max_entry.grid(row=2, column=1, padx=5, pady=5)

            return self.min_entry  # Initial focus

        def validate(self):
            min_val = self.min_entry.get().strip()
            max_val = self.max_entry.get().strip()

            try:
                if min_val:
                    self.x_min = float(min_val)
                if max_val:
                    self.x_max = float(max_val)

                # Ensure min is less than max if both are provided
                if self.x_min is not None and self.x_max is not None and self.x_min >= self.x_max:
                    messagebox.showerror("Invalid Range", "Minimum value must be less than maximum value.")
                    return False

                return True
            except ValueError:
                messagebox.showerror("Invalid Input", "Please enter valid numbers or leave fields empty.")
                return False

        def apply(self):
            # Values are already stored in self.x_min and self.x_max
            pass

    # Show the custom dialog
    dialog = CustomRangeDialog(root, "X-Axis Range", target_log)
    x_min = dialog.x_min
    x_max = dialog.x_max

    for well_data in all_wells_data:
        well_name = well_data['well_name']
        depth = well_data['depth']
        target = well_data['target']
        normalized_eei = well_data['normalized_eei']
        angle = well_data['angle']
        vol_wetclay = well_data.get('vol_wetclay')

        # Check if we have all necessary data
        if depth is None or target is None or normalized_eei is None:
            print(f"Skipping well {well_name} due to missing data.")
            continue

        # Detect analysis type from angle parameter format
        if isinstance(angle, str) and 'n=' in angle and 'phi=' in angle:
            if 'CPEI' in str(angle).upper():
                analysis_type = 'CPEI'
            elif 'PEIL' in str(angle).upper():
                analysis_type = 'PEIL'
            else:
                # Default to CPEI for n,phi format without explicit type
                analysis_type = 'CPEI' if 'n=' in angle else 'EEI'
        else:
            analysis_type = 'EEI'

        # Calculate correlation, automatically omitting NaN values
        correlation = nanaware_corrcoef(normalized_eei, target)

        # Get the analysis depth range and add buffer
        top_depth, bottom_depth = depth_ranges[well_name]
        y_min, y_max = top_depth - 30, bottom_depth + 30

        # Determine the number of subplots based on available data
        if vol_wetclay is not None:
            fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(20, 10))
        else:
            fig, (ax2, ax3) = plt.subplots(1, 2, figsize=(15, 10))
            print(f"Warning: VOL_WETCLAY not available for well {well_name}, plotting only 2 columns.")

        fig.suptitle(f'{well_name}: {analysis_type} vs {target_log} Analysis', fontsize=16)

        # First column: Depth vs VOL_WETCLAY (if available)
        if vol_wetclay is not None:
            valid_vcl_mask = ~np.isnan(vol_wetclay)
            ax1.plot(vol_wetclay[valid_vcl_mask], depth[valid_vcl_mask], label='VOL_WETCLAY', color='green')
            ax1.set_xlabel('VOL_WETCLAY')
            ax1.set_ylabel('Depth')
            ax1.set_ylim(y_max, y_min)  # Correct order for depth to increase downwards
            ax1.set_xlim(0, 1)  # Set x-axis limits for VOL_WETCLAY
            ax1.axhline(y=top_depth, color='r', linestyle='--', label='Top')
            ax1.axhline(y=bottom_depth, color='b', linestyle='--', label='Bottom')
            ax1.legend()

        # Second column: Depth vs Target Log and Normalized EEI
        valid_mask = ~np.isnan(target) & ~np.isnan(normalized_eei) & ~np.isnan(depth)
        ax2.plot(np.ma.masked_array(target, ~valid_mask),
                 np.ma.masked_array(depth, ~valid_mask),
                 label=target_log, color='blue')
        # Format angle display based on analysis type
        if analysis_type == 'EEI':
            angle_display = f'{safe_format_float(angle, precision=1, default="N/A")}°'
        else:
            angle_display = str(angle) if angle is not None else "N/A"

        ax2.plot(np.ma.masked_array(normalized_eei, ~valid_mask),
                 np.ma.masked_array(depth, ~valid_mask),
                 label=f'{analysis_type} ({angle_display})', color='red')
        ax2.set_xlabel('Value')
        ax2.set_ylabel('Depth')
        ax2.set_ylim(y_max, y_min)  # Correct order for depth to increase downwards

        # Set x-axis limits based on user input or global percentiles
        try:
            if x_min is not None and x_max is not None:
                # Both min and max provided by user
                final_x_min, final_x_max = x_min, x_max
            elif x_min is not None:
                # Only min provided, use global max as fallback
                final_x_min, final_x_max = x_min, global_max
            elif x_max is not None:
                # Only max provided, use global min as fallback
                final_x_min, final_x_max = global_min, x_max
            else:
                # No user input, use global percentiles for consistent scaling across all wells
                final_x_min, final_x_max = global_min, global_max

            # Validate that final limits are finite
            if not (np.isfinite(final_x_min) and np.isfinite(final_x_max)):
                logger.warning(f"Final axis limits are not finite for well {well_name}: "
                              f"min={final_x_min}, max={final_x_max}. Using fallback limits (0, 1).")
                final_x_min, final_x_max = 0.0, 1.0

            # Ensure min < max
            if final_x_min >= final_x_max:
                logger.warning(f"Final x_min ({final_x_min}) >= x_max ({final_x_max}) for well {well_name}. "
                              f"Adding small buffer to max value.")
                final_x_max = final_x_min + 1.0

            ax2.set_xlim(final_x_min, final_x_max)
            logger.debug(f"Set axis limits for well {well_name}: x_min={final_x_min:.4f}, x_max={final_x_max:.4f}")

        except Exception as e:
            logger.error(f"Error setting axis limits for well {well_name}: {str(e)}. Using fallback limits (0, 1).")
            ax2.set_xlim(0.0, 1.0)
        ax2.axhline(y=top_depth, color='r', linestyle='--', label='Top')
        ax2.axhline(y=bottom_depth, color='b', linestyle='--', label='Bottom')
        ax2.legend()

        # Third column: Scatter plot of Normalized calculated curve vs Target Log
        ax3.scatter(np.ma.masked_array(normalized_eei, ~valid_mask),
                    np.ma.masked_array(target, ~valid_mask),
                    alpha=0.5)
        ax3.set_xlabel(f'{analysis_type} ({angle_display})')
        ax3.set_ylabel(target_log)

        # Calculate optimal axis limits using advanced scaling algorithm with global percentiles fallback
        try:
            x_min, x_max, y_min, y_max = calculate_optimal_crossplot_limits(
                normalized_eei[valid_mask],
                target[valid_mask],
                correlation,
                global_x_min=global_min,
                global_x_max=global_max,
                global_y_min=global_min,  # Use same global percentiles for y-axis
                global_y_max=global_max,
                well_name=well_name
            )

            # Validate that final limits are finite before setting
            if not all(np.isfinite([x_min, x_max, y_min, y_max])):
                logger.warning(f"Crossplot limits are not finite for well {well_name}: "
                              f"x_min={x_min}, x_max={x_max}, y_min={y_min}, y_max={y_max}. "
                              f"Using global percentiles as fallback.")
                x_min, x_max, y_min, y_max = global_min, global_max, global_min, global_max

            ax3.set_xlim(x_min, x_max)
            ax3.set_ylim(y_min, y_max)
            logger.debug(f"Set crossplot limits for well {well_name}: "
                        f"x_min={x_min:.4f}, x_max={x_max:.4f}, y_min={y_min:.4f}, y_max={y_max:.4f}")

        except Exception as e:
            logger.error(f"Error setting crossplot limits for well {well_name}: {str(e)}. Using global percentiles.")
            ax3.set_xlim(global_min, global_max)
            ax3.set_ylim(global_min, global_max)

        # Calculate linear regression
        x_for_regression = normalized_eei[valid_mask]
        y_for_regression = target[valid_mask]
        if len(x_for_regression) > 1:  # Need at least 2 points for regression
            # Ensure no NaN values in the data
            reg_mask = ~np.isnan(x_for_regression) & ~np.isnan(y_for_regression)
            if np.sum(reg_mask) > 1:  # Still need at least 2 valid points after removing NaNs
                regression = stats.linregress(x_for_regression[reg_mask], y_for_regression[reg_mask])
                # Plot regression line using optimized axis limits
                x_line = np.array([x_min, x_max])
                y_line = regression.slope * x_line + regression.intercept
                ax3.plot(x_line, y_line, 'r-', linewidth=2, label='Linear regression')

                # Add regression equation and R² to the plot
                equation = f'y = {regression.slope:.4f}x + {regression.intercept:.4f}'
                r_squared = regression.rvalue**2
                ax3.text(0.05, 0.85, equation, transform=ax3.transAxes, verticalalignment='top')
                ax3.text(0.05, 0.75, f'R² = {r_squared:.4f}', transform=ax3.transAxes, verticalalignment='top')

        # Add a diagonal line (y = x) for reference using the appropriate range
        diag_min = max(x_min, y_min)
        diag_max = min(x_max, y_max)
        if diag_min < diag_max:  # Only draw if there's a valid range
            ax3.plot([diag_min, diag_max], [diag_min, diag_max], 'k--', alpha=0.5, label='y = x')

        # Add correlation value to the plot
        ax3.text(0.05, 0.95, f'Correlation: {safe_format_float(correlation, precision=4, default="N/A")}',
                 transform=ax3.transAxes, verticalalignment='top')

        # Add legend
        ax3.legend(loc='lower right')

        # Add correlation value to the plot title
        fig.suptitle(f'{well_name}: {analysis_type} vs {target_log} Analysis\nCorrelation: {safe_format_float(correlation, precision=4, default="N/A")}', fontsize=16)

        plt.tight_layout()
        plt.show()

def individual_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name, depth_ranges, analysis_method, calcmethod, k_method, k_value, alternative_mnemonics=None):
    """
    Perform individual well analysis for EEI, CPEI, or PEIL.

    Args:
        las_files: List of LAS file objects
        log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
        target_log_generic_name: Generic name of the target log
        depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
        analysis_method: 1=EEI, 2=CPEI, 3=PEIL
        calcmethod: Method to use for EEI calculation (only used for EEI)
        k_method: Method for k value (only used for EEI)
        k_value: Constant k value (only used for EEI)
        alternative_mnemonics: Dictionary to track alternative mnemonics selected by the user

    Returns:
        Tuple of (all_wells_results, all_wells_data)
    """
    all_wells_results = []
    all_wells_data = []

    # Initialize alternative_mnemonics if not provided
    if alternative_mnemonics is None:
        alternative_mnemonics = {}

    required_base_generic_logs = ['DEPTH', 'DT', 'DTS', 'RHOB']

    for las in las_files:
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges[well_name]

        # Find actual mnemonics for this specific well
        current_well_columns = find_default_columns(las, log_keywords_for_finding_cols)

        actual_mnemonics_for_base_logs = {}
        missing_base_logs_info = []
        for generic_name in required_base_generic_logs:
            actual_mnemonic = current_well_columns.get(generic_name)
            if actual_mnemonic is None or actual_mnemonic not in las.curves:
                missing_base_logs_info.append(f"{generic_name} (searched aliases: {log_keywords_for_finding_cols.get(generic_name, [])})")
            else:
                actual_mnemonics_for_base_logs[generic_name] = actual_mnemonic

        if missing_base_logs_info:
            print(f"Warning: Missing or invalid base logs ({', '.join(missing_base_logs_info)}) for well {well_name}. Skipping this well for individual analysis.")
            all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
            all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
            continue

        target_log_actual_mnemonic = current_well_columns.get(target_log_generic_name)
        # If generic name itself is a curve (e.g. user typed 'GR' and 'GR' exists, but 'GR' is not in log_keywords['GR'])
        if target_log_actual_mnemonic is None and target_log_generic_name in las.curves:
            target_log_actual_mnemonic = target_log_generic_name
            print(f"Info: Target log '{target_log_generic_name}' used directly as mnemonic for well {well_name}.")

        # Check if target log is missing and offer interactive fallback selection
        if target_log_actual_mnemonic is None or target_log_actual_mnemonic not in las.curves:
            # Check if we already have an alternative for this well
            well_key = f"{well_name}:{target_log_generic_name}"
            if well_key in alternative_mnemonics:
                alternative = alternative_mnemonics[well_key]
                if alternative is not None:
                    target_log_actual_mnemonic = alternative
                    print(f"Info: Using previously selected alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}'.")
                else:
                    print(f"Info: Well '{well_name}' was previously marked to be skipped for target log '{target_log_generic_name}'.")
                    all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                    all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                    continue
            else:
                # Display dialog for selecting an alternative
                print(f"Warning: Target log '{target_log_generic_name}' (resolved mnemonic '{target_log_actual_mnemonic or 'None'}', searched aliases: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])}) not found in well {well_name}.")
                alternative = select_alternative_mnemonic(las, target_log_generic_name, well_name)

                # Store the selected alternative (or None if skipped)
                alternative_mnemonics[well_key] = alternative

                if alternative is None:
                    print(f"Info: User chose to skip well '{well_name}' for target log '{target_log_generic_name}'.")
                    all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                    all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                    continue
                else:
                    target_log_actual_mnemonic = alternative
                    print(f"Info: Using alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}'.")

        vcl_actual_mnemonic = current_well_columns.get('VCL') # VCL is optional, might be None. find_default_columns would have printed if not found.

        # Perform optimization based on analysis method
        if analysis_method == 1:  # EEI Analysis
            optimum_angle, max_correlation, angles, correlations = calculate_eei_optimum_angle(
                las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic,
                top_depth, bottom_depth, calcmethod, k_method, k_value
            )

            if optimum_angle is None or max_correlation is None:
                print(f"Skipping well {well_name} due to invalid data from EEI optimum angle calculation.")
                all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                continue

            print(f"Optimum angle for Well {well_name}: {optimum_angle}°")
            print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {safe_format_float(max_correlation, precision=4, default='N/A')}")

            # Plot EEI-Target Log Correlation vs Angle
            plt.figure(figsize=(12, 6))
            plt.plot(angles, correlations)
            plt.axvline(x=optimum_angle, color='r', linestyle='--', label=f'Optimum Angle: {optimum_angle}°')
            plt.xlabel('Angle (degrees)')
            plt.ylabel('Correlation Coefficient')
            plt.title(f'EEI-{target_log_generic_name} Correlation vs Angle for Well: {well_name}\nDepth range: {safe_format_float(top_depth, precision=2, default="N/A")} - {safe_format_float(bottom_depth, precision=2, default="N/A")}')
            plt.legend()
            plt.grid(True)
            plt.show()

            # Calculate EEI using the optimum angle
            depth, target, normalized_eei, vol_wetclay = calculate_eei(
                las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, vcl_actual_mnemonic,
                top_depth, bottom_depth, optimum_angle, calcmethod, k_method, k_value
            )

            all_wells_results.append({
                'well_name': well_name,
                'optimum_angle': optimum_angle,
                'max_correlation': max_correlation,
                'top_depth': top_depth,
                'bottom_depth': bottom_depth
            })

            all_wells_data.append({
                'well_name': well_name,
                'depth': depth,
                'target': target,
                'normalized_eei': normalized_eei,
                'angle': optimum_angle,
                'vol_wetclay': vol_wetclay
            })

        elif analysis_method == 2:  # CPEI Analysis
            optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix = calculate_cpei_optimum_parameters(
                las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, top_depth, bottom_depth
            )

            if optimal_n is None or optimal_phi is None or max_correlation is None:
                print(f"Skipping well {well_name} due to invalid data from CPEI optimization.")
                all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                continue

            # Safely format CPEI parameters with None value checks
            try:
                n_str = f"{optimal_n:.1f}" if optimal_n is not None else "N/A"
                phi_str = f"{optimal_phi}°" if optimal_phi is not None else "N/A"
                print(f"Optimal CPEI parameters for Well {well_name}: n={n_str}, phi={phi_str}")
            except (ValueError, TypeError) as e:
                print(f"Error formatting CPEI parameters for Well {well_name}: optimal_n={optimal_n}, optimal_phi={optimal_phi}. Error: {str(e)}")
                print(f"Optimal CPEI parameters for Well {well_name}: n={optimal_n}, phi={optimal_phi}")

            # Ensure max_correlation is properly formatted as a float
            try:
                correlation_value = float(max_correlation) if max_correlation is not None else 0.0
                print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {correlation_value:.4f}")
            except (ValueError, TypeError) as e:
                print(f"Error formatting correlation value: {max_correlation}. Error: {str(e)}")
                print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {max_correlation}")

            # Plot CPEI correlation heatmap
            plt.figure(figsize=(12, 8))
            plt.imshow(correlation_matrix, aspect='auto', origin='lower', cmap='viridis')
            plt.colorbar(label='Correlation Coefficient')
            plt.xlabel('Phi (degrees)')
            plt.ylabel('n (exponent)')
            plt.title(f'CPEI-{target_log_generic_name} Correlation Matrix for Well: {well_name}\nDepth range: {safe_format_float(top_depth, precision=2, default="N/A")} - {safe_format_float(bottom_depth, precision=2, default="N/A")}')

            # Set tick labels
            phi_ticks = range(0, len(phi_values), 30)  # Every 30 degrees
            n_ticks = range(0, len(n_values), 5)  # Every 0.5 in n
            plt.xticks(phi_ticks, [phi_values[i] for i in phi_ticks])
            plt.yticks(n_ticks, [safe_format_float(n_values[i], precision=1, default='N/A') for i in n_ticks])

            # Mark optimal point
            optimal_phi_idx = list(phi_values).index(optimal_phi)
            optimal_n_idx = list(n_values).index(optimal_n)
            # Safely format the plot label with None value checks
            try:
                label_text = f'Optimal: n={optimal_n:.1f}, φ={optimal_phi}°' if optimal_n is not None and optimal_phi is not None else f'Optimal: n={optimal_n}, φ={optimal_phi}'
            except (ValueError, TypeError):
                label_text = f'Optimal: n={optimal_n}, φ={optimal_phi}'
            plt.plot(optimal_phi_idx, optimal_n_idx, 'r*', markersize=15, label=label_text)

            # Add annotation with maximum correlation value
            max_corr_text = safe_format_float(max_correlation, precision=4, default='N/A')
            annotation_text = f'Max Correlation: {max_corr_text}'
            plt.annotate(annotation_text,
                        xy=(optimal_phi_idx, optimal_n_idx),
                        xytext=(optimal_phi_idx + 10, optimal_n_idx + 2),
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                        arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.2'),
                        fontsize=10, fontweight='bold')

            # Add summary text box in the corner
            summary_text = f'CPEI Individual Well Summary:\n'
            summary_text += f'Well: {well_name}\n'
            summary_text += f'Optimal n: {safe_format_float(optimal_n, precision=1, default="N/A")}\n'
            summary_text += f'Optimal φ: {safe_format_float(optimal_phi, precision=0, default="N/A")}°\n'
            summary_text += f'Max Correlation: {max_corr_text}\n'
            summary_text += f'Target: {target_log_generic_name}'

            plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8),
                    verticalalignment='top', fontsize=9, fontfamily='monospace')

            plt.legend()
            plt.tight_layout()
            plt.show()

            # For CPEI, we'll store the parameters differently since there's no single "angle"
            # Safely format parameter strings for storage
            try:
                angle_str = f"n={optimal_n:.1f}, phi={optimal_phi}°" if optimal_n is not None and optimal_phi is not None else f"n={optimal_n}, phi={optimal_phi}"
            except (ValueError, TypeError):
                angle_str = f"n={optimal_n}, phi={optimal_phi}"

            all_wells_results.append({
                'well_name': well_name,
                'optimum_angle': angle_str,  # Store as string for compatibility
                'max_correlation': max_correlation,
                'top_depth': top_depth,
                'bottom_depth': bottom_depth
            })

            # Calculate CPEI using optimal parameters for plotting
            depth, target, normalized_cpei, vol_wetclay = calculate_cpei_for_plotting(
                las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, vcl_actual_mnemonic,
                top_depth, bottom_depth, optimal_n, optimal_phi
            )

            all_wells_data.append({
                'well_name': well_name,
                'depth': depth,
                'target': target,
                'normalized_eei': normalized_cpei,  # Use normalized_eei field for compatibility
                'angle': angle_str,
                'vol_wetclay': vol_wetclay
            })

        elif analysis_method == 3:  # PEIL Analysis
            optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix = calculate_peil_optimum_parameters(
                las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, top_depth, bottom_depth
            )

            if optimal_n is None or optimal_phi is None or max_correlation is None:
                print(f"Skipping well {well_name} due to invalid data from PEIL optimization.")
                all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                continue

            # Safely format PEIL parameters with None value checks
            try:
                n_str = f"{optimal_n:.1f}" if optimal_n is not None else "N/A"
                phi_str = f"{optimal_phi}°" if optimal_phi is not None else "N/A"
                print(f"Optimal PEIL parameters for Well {well_name}: n={n_str}, phi={phi_str}")
            except (ValueError, TypeError) as e:
                print(f"Error formatting PEIL parameters for Well {well_name}: optimal_n={optimal_n}, optimal_phi={optimal_phi}. Error: {str(e)}")
                print(f"Optimal PEIL parameters for Well {well_name}: n={optimal_n}, phi={optimal_phi}")

            # Ensure max_correlation is properly formatted as a float
            try:
                correlation_value = float(max_correlation) if max_correlation is not None else 0.0
                print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {correlation_value:.4f}")
            except (ValueError, TypeError) as e:
                print(f"Error formatting correlation value: {max_correlation}. Error: {str(e)}")
                print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {max_correlation}")

            # Plot PEIL correlation heatmap
            plt.figure(figsize=(12, 8))
            plt.imshow(correlation_matrix, aspect='auto', origin='lower', cmap='viridis')
            plt.colorbar(label='Correlation Coefficient')
            plt.xlabel('Phi (degrees)')
            plt.ylabel('n (exponent)')
            plt.title(f'PEIL-{target_log_generic_name} Correlation Matrix for Well: {well_name}\nDepth range: {safe_format_float(top_depth, precision=2, default="N/A")} - {safe_format_float(bottom_depth, precision=2, default="N/A")}')

            # Set tick labels
            phi_ticks = range(0, len(phi_values), 30)  # Every 30 degrees
            n_ticks = range(0, len(n_values), 5)  # Every 0.5 in n
            plt.xticks(phi_ticks, [phi_values[i] for i in phi_ticks])
            plt.yticks(n_ticks, [safe_format_float(n_values[i], precision=1, default='N/A') for i in n_ticks])

            # Mark optimal point
            optimal_phi_idx = list(phi_values).index(optimal_phi)
            optimal_n_idx = list(n_values).index(optimal_n)
            # Safely format the plot label with None value checks
            try:
                label_text = f'Optimal: n={optimal_n:.1f}, φ={optimal_phi}°' if optimal_n is not None and optimal_phi is not None else f'Optimal: n={optimal_n}, φ={optimal_phi}'
            except (ValueError, TypeError):
                label_text = f'Optimal: n={optimal_n}, φ={optimal_phi}'
            plt.plot(optimal_phi_idx, optimal_n_idx, 'r*', markersize=15, label=label_text)

            # Add annotation with maximum correlation value
            max_corr_text = safe_format_float(max_correlation, precision=4, default='N/A')
            annotation_text = f'Max Correlation: {max_corr_text}'
            plt.annotate(annotation_text,
                        xy=(optimal_phi_idx, optimal_n_idx),
                        xytext=(optimal_phi_idx + 10, optimal_n_idx + 2),
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                        arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.2'),
                        fontsize=10, fontweight='bold')

            # Add summary text box in the corner
            summary_text = f'PEIL Individual Well Summary:\n'
            summary_text += f'Well: {well_name}\n'
            summary_text += f'Optimal n: {safe_format_float(optimal_n, precision=1, default="N/A")}\n'
            summary_text += f'Optimal φ: {safe_format_float(optimal_phi, precision=0, default="N/A")}°\n'
            summary_text += f'Max Correlation: {max_corr_text}\n'
            summary_text += f'Target: {target_log_generic_name}'

            plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8),
                    verticalalignment='top', fontsize=9, fontfamily='monospace')

            plt.legend()
            plt.tight_layout()
            plt.show()

            # For PEIL, we'll store the parameters differently since there's no single "angle"
            # Safely format parameter strings for storage
            try:
                angle_str = f"n={optimal_n:.1f}, phi={optimal_phi}°" if optimal_n is not None and optimal_phi is not None else f"n={optimal_n}, phi={optimal_phi}"
            except (ValueError, TypeError):
                angle_str = f"n={optimal_n}, phi={optimal_phi}"

            all_wells_results.append({
                'well_name': well_name,
                'optimum_angle': angle_str,  # Store as string for compatibility
                'max_correlation': max_correlation,
                'top_depth': top_depth,
                'bottom_depth': bottom_depth
            })

            # Calculate PEIL using optimal parameters for plotting
            depth, target, normalized_peil, vol_wetclay = calculate_peil_for_plotting(
                las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, vcl_actual_mnemonic,
                top_depth, bottom_depth, optimal_n, optimal_phi
            )

            all_wells_data.append({
                'well_name': well_name,
                'depth': depth,
                'target': target,
                'normalized_eei': normalized_peil,  # Use normalized_eei field for compatibility
                'angle': angle_str,
                'vol_wetclay': vol_wetclay
            })

    return all_wells_results, all_wells_data

def merged_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name, depth_ranges, analysis_method, calcmethod, k_method, k_value, alternative_mnemonics=None):
    logger.info("Starting merged well analysis...")
    """
    Perform merged well analysis for EEI, CPEI, or PEIL.

    Args:
        las_files: List of LAS file objects
        log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
        target_log_generic_name: Generic name of the target log
        depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
        analysis_method: 1=EEI, 2=CPEI, 3=PEIL
        calcmethod: Method to use for EEI calculation (only used for EEI)
        k_method: Method for k value (only used for EEI)
        k_value: Constant k value (only used for EEI)
        alternative_mnemonics: Dictionary to track alternative mnemonics selected by the user

    Returns:
        List of well data for plotting
    """
    merged_depth_list = []
    merged_dt_list = []
    merged_dts_list = []
    merged_rhob_list = []
    merged_target_list = []

    # Initialize alternative_mnemonics if not provided
    if alternative_mnemonics is None:
        alternative_mnemonics = {}

    required_base_generic_logs = ['DEPTH', 'DT', 'DTS', 'RHOB']
    processed_wells_for_merge = 0

    for las in las_files:
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges[well_name]

        current_well_cols = find_default_columns(las, log_keywords_for_finding_cols)

        actual_mnemonics_for_merge = {}
        missing_logs_for_this_well_merge = []
        for generic_name in required_base_generic_logs:
            mnemonic = current_well_cols.get(generic_name)
            if mnemonic is None or mnemonic not in las.curves:
                missing_logs_for_this_well_merge.append(f"{generic_name} (searched aliases: {log_keywords_for_finding_cols.get(generic_name, [])})")
            else:
                actual_mnemonics_for_merge[generic_name] = mnemonic

        # Check for missing required base logs (these can't be substituted)
        if any(log in missing_logs_for_this_well_merge for log in required_base_generic_logs):
            print(f"Warning: For merged analysis, well {well_name} is missing required base logs: {', '.join(missing_logs_for_this_well_merge)}. Skipping this well's data for merge.")
            continue # Skip this well for merging

        target_actual_mnemonic_for_merge = current_well_cols.get(target_log_generic_name)
        if target_actual_mnemonic_for_merge is None and target_log_generic_name in las.curves: # Direct check
             target_actual_mnemonic_for_merge = target_log_generic_name
             print(f"Info: Target log '{target_log_generic_name}' used directly as mnemonic for well {well_name} in merge.")

        # Check if target log is missing and offer interactive fallback selection
        if target_actual_mnemonic_for_merge is None or target_actual_mnemonic_for_merge not in las.curves:
            # Check if we already have an alternative for this well
            well_key = f"{well_name}:{target_log_generic_name}"
            if well_key in alternative_mnemonics:
                alternative = alternative_mnemonics[well_key]
                if alternative is not None:
                    target_actual_mnemonic_for_merge = alternative
                    print(f"Info: Using previously selected alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}' for merged analysis.")
                else:
                    print(f"Info: Well '{well_name}' was previously marked to be skipped for target log '{target_log_generic_name}'. Skipping for merged analysis.")
                    continue
            else:
                # Display dialog for selecting an alternative
                print(f"Warning: Target log '{target_log_generic_name}' (resolved mnemonic '{target_actual_mnemonic_for_merge or 'None'}', searched aliases: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])}) not found in well {well_name}.")
                alternative = select_alternative_mnemonic(las, target_log_generic_name, well_name)

                # Store the selected alternative (or None if skipped)
                alternative_mnemonics[well_key] = alternative

                if alternative is None:
                    print(f"Info: User chose to skip well '{well_name}' for target log '{target_log_generic_name}' in merged analysis.")
                    continue
                else:
                    target_actual_mnemonic_for_merge = alternative
                    print(f"Info: Using alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}' for merged analysis.")

        depth_data = np.array(las[actual_mnemonics_for_merge['DEPTH']].data)
        dt_data = np.array(las[actual_mnemonics_for_merge['DT']].data)
        dts_data = np.array(las[actual_mnemonics_for_merge['DTS']].data)
        rhob_data = np.array(las[actual_mnemonics_for_merge['RHOB']].data)
        target_data = np.array(las[target_actual_mnemonic_for_merge].data)

        # Find nearest indices for top and bottom depths
        top_index = find_nearest_index(depth_data, top_depth)
        bottom_index = find_nearest_index(depth_data, bottom_depth)

        # Ensure top_index is smaller than bottom_index
        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index

        # Slice the arrays using the indices
        merged_depth_list.extend(depth_data[top_index:bottom_index+1])
        merged_dt_list.extend(dt_data[top_index:bottom_index+1])
        merged_dts_list.extend(dts_data[top_index:bottom_index+1])
        merged_rhob_list.extend(rhob_data[top_index:bottom_index+1])
        merged_target_list.extend(target_data[top_index:bottom_index+1])
        processed_wells_for_merge += 1

    if processed_wells_for_merge == 0 or not merged_depth_list: # Check if any data was actually merged
        print("No data could be merged for merged analysis (e.g. all wells missed required logs). Cannot proceed with merged optimum angle calculation.")
        return [] # Return empty list for all_wells_data

    merged_depth = np.array(merged_depth_list)
    merged_dt = np.array(merged_dt_list)
    merged_dts = np.array(merged_dts_list)
    merged_rhob = np.array(merged_rhob_list)
    merged_target = np.array(merged_target_list)

    # Validate merged data before proceeding with optimization
    if any(x is None for x in [merged_depth, merged_dt, merged_dts, merged_rhob, merged_target]) or \
       any(not x.size for x in [merged_depth, merged_dt, merged_dts, merged_rhob, merged_target] if x is not None and hasattr(x, 'size')):
        logger.error("Merged data arrays (depth, dt, dts, rhob, or target) are None or empty after internal merging. Aborting merged optimization.")
        return [] # Return empty list, consistent with other failure paths
    logger.info(f"Merged data successfully created internally from {processed_wells_for_merge} wells. Total points: {len(merged_depth)}. Proceeding with optimization...")


    # Convert slowness to velocity (assuming microseconds/ft to m/s conversion)
    pvel = 304800 / merged_dt  # Convert microseconds/ft to m/s
    svel = 304800 / merged_dts  # Convert microseconds/ft to m/s

    # Determine k value based on method selected
    if k_method == 1:
        # Calculate k from velocity ratio
        velocity_ratio = svel / pvel
        k = np.nanmean(velocity_ratio**2)

        # Validate calculated k value
        if k is None or not np.isfinite(k) or k <= 0:
            logger.warning(f"Invalid calculated k value: {k}, using default k=0.25")
            k = 0.25  # Default reasonable k value

        logger.info(f"Calculated k value from logs (merged): {safe_format_float(k, precision=4)}")
    else:
        k = k_value

        # Validate provided k value
        if k is None or not np.isfinite(k) or k <= 0:
            logger.warning(f"Invalid provided k value: {k}, using default k=0.25")
            k = 0.25  # Default reasonable k value

        logger.info(f"Using constant k value (merged): {safe_format_float(k, precision=4)}")

    if analysis_method == 1:  # EEI Analysis
        angles = range(-90, 91)
        correlations = []

        for angle in angles:
            try:
                eei, _, _ = eeimpcalc(pvel, svel, merged_rhob, angle, k, calcmethod=calcmethod)

                # Validate EEI calculation result
                if eei is None or not hasattr(eei, '__len__'):
                    correlation = np.nan
                else:
                    correlation = nanaware_corrcoef(eei, merged_target)

                correlations.append(correlation)

            except Exception as e:
                logger.warning(f"Error calculating EEI for angle {angle} in merged analysis: {str(e)}")
                correlations.append(np.nan)

        # Find the optimum angle
        correlations = np.array(correlations)
        valid_correlations = correlations[np.isfinite(correlations)]

        if len(valid_correlations) == 0:
            logger.error("All correlations for merged data are NaN. Unable to find optimum angle for merged wells.")
            optimum_angle_merged = 0 # Default or placeholder
            max_correlation_merged = np.nan
        else:
            max_idx = np.nanargmax(correlations)
            optimum_angle_merged = angles[max_idx]
            max_correlation_merged = correlations[max_idx]

        print(f"Optimum angle for Merged Wells: {optimum_angle_merged}°")
        print(f"Maximum correlation coefficient with {target_log_generic_name}: {safe_format_float(max_correlation_merged, precision=4, default='N/A')}")

        # Plot EEI-Target Log Correlation vs Angle
        plt.figure(figsize=(12, 6))
        plt.plot(angles, correlations)
        plt.axvline(x=optimum_angle_merged, color='r', linestyle='--', label=f'Optimum Angle: {optimum_angle_merged}°')
        plt.xlabel('Angle (degrees)')
        plt.ylabel('Correlation Coefficient')
        plt.title(f'EEI-{target_log_generic_name} Correlation vs Angle for Merged Wells')
        plt.legend()
        plt.grid(True)
        plt.show()

    elif analysis_method == 2:  # CPEI Analysis
        print("Starting CPEI optimization for merged wells...")

        # Define parameter ranges for CPEI
        n_values = np.arange(0.1, 2.1, 0.1)  # n from 0.1 to 2.0 with step 0.1
        phi_values = range(-90, 91)  # phi from -90° to +90°

        correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)

        for i, n in enumerate(n_values):
            for j, phi in enumerate(phi_values):
                try:
                    # Validate inputs before calculation
                    validation = validate_cpei_peil_inputs(pvel, svel, merged_rhob, n, phi, "CPEI")
                    if not validation['valid']:
                        logger.warning(f"CPEI validation failed for merged data n={safe_format_float(n, 1)}, phi={phi}: {'; '.join(validation['errors'])}")
                        correlation_matrix[i, j] = np.nan
                        continue

                    cpei = calculate_cpei(pvel, svel, merged_rhob, n, phi)

                    # Validate CPEI output
                    if cpei is None:
                        logger.warning(f"CPEI calculation returned None for merged data n={safe_format_float(n, 1)}, phi={phi}")
                        correlation_matrix[i, j] = np.nan
                        continue

                    if hasattr(cpei, 'size') and cpei.size == 0:
                        logger.warning(f"CPEI calculation returned empty array for merged data n={safe_format_float(n, 1)}, phi={phi}")
                        correlation_matrix[i, j] = np.nan
                        continue

                    correlation = nanaware_corrcoef(cpei, merged_target)
                    correlation_matrix[i, j] = correlation

                except Exception as e:
                    error_msg = f"Error calculating CPEI for merged data n={safe_format_float(n, 1)}, phi={phi}: {str(e)}"
                    logger.error(error_msg)
                    print(f"Warning: {error_msg}")
                    correlation_matrix[i, j] = np.nan

        # Find optimal parameters
        if np.all(np.isnan(correlation_matrix)):
            print("All correlations are NaN. Unable to find optimum parameters for merged CPEI.")
            optimal_n_merged = 1.0
            optimal_phi_merged = 0
            max_correlation_merged = np.nan
        else:
            max_idx = np.unravel_index(np.nanargmax(correlation_matrix), correlation_matrix.shape)
            optimal_n_merged = n_values[max_idx[0]]
            optimal_phi_merged = phi_values[max_idx[1]]
            max_correlation_merged = correlation_matrix[max_idx]

        print(f"CPEI optimization complete for merged wells:")
        # Safely format merged CPEI parameters using helper functions
        print(f"  Optimal n: {safe_format_float(optimal_n_merged, precision=1, default='N/A')}")

        optimal_phi_merged_val_cpei = safe_format_float(optimal_phi_merged, precision=0, default="N/A")
        phi_display_cpei = f"{optimal_phi_merged_val_cpei}°" if optimal_phi_merged_val_cpei != "N/A" else "N/A"
        print(f"  Optimal phi: {phi_display_cpei}")

        print(f"  Maximum correlation coefficient with {target_log_generic_name}: {safe_format_float(max_correlation_merged, precision=4, default='N/A')}")

        # Plot CPEI correlation heatmap for merged wells
        plt.figure(figsize=(12, 8))
        plt.imshow(correlation_matrix, aspect='auto', origin='lower', cmap='viridis')
        plt.colorbar(label='Correlation Coefficient')
        plt.xlabel('Phi (degrees)')
        plt.ylabel('n (exponent)')
        plt.title(f'CPEI-{target_log_generic_name} Correlation Matrix for Merged Wells')

        # Set tick labels
        phi_ticks = range(0, len(phi_values), 30)  # Every 30 degrees
        n_ticks = range(0, len(n_values), 5)  # Every 0.5 in n
        plt.xticks(phi_ticks, [phi_values[i] for i in phi_ticks])
        plt.yticks(n_ticks, [safe_format_float(n_values[i], precision=1, default='N/A') for i in n_ticks])

        # Mark optimal point
        optimal_phi_idx = list(phi_values).index(optimal_phi_merged)
        optimal_n_idx = list(n_values).index(optimal_n_merged)
        # Safely format the plot label using helper function
        label_text = f'Optimal: {safe_format_parameter_string(optimal_n_merged, optimal_phi_merged, default="n=N/A, phi=N/A")}'
        plt.plot(optimal_phi_idx, optimal_n_idx, 'r*', markersize=15, label=label_text)

        # Add annotation with maximum correlation value
        max_corr_text = safe_format_float(max_correlation_merged, precision=4, default='N/A')
        annotation_text = f'Max Correlation: {max_corr_text}'
        plt.annotate(annotation_text,
                    xy=(optimal_phi_idx, optimal_n_idx),
                    xytext=(optimal_phi_idx + 10, optimal_n_idx + 2),
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.2'),
                    fontsize=10, fontweight='bold')

        # Add summary text box in the corner
        summary_text = f'CPEI Merged Analysis Summary:\n'
        summary_text += f'Optimal n: {safe_format_float(optimal_n_merged, precision=1, default="N/A")}\n'
        summary_text += f'Optimal φ: {safe_format_float(optimal_phi_merged, precision=0, default="N/A")}°\n'
        summary_text += f'Max Correlation: {max_corr_text}\n'
        summary_text += f'Target: {target_log_generic_name}'

        plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8),
                verticalalignment='top', fontsize=9, fontfamily='monospace')

        plt.legend()
        plt.tight_layout()
        plt.show()

        # Store parameters as string for compatibility with safe formatting
        optimum_angle_merged = safe_format_parameter_string(optimal_n_merged, optimal_phi_merged, default=f"n={str(optimal_n_merged)}, phi={str(optimal_phi_merged)}")

    elif analysis_method == 3:  # PEIL Analysis
        print("Starting PEIL optimization for merged wells...")

        # Define parameter ranges for PEIL
        n_values = np.arange(0.1, 2.1, 0.1)  # n from 0.1 to 2.0 with step 0.1
        phi_values = range(-90, 91)  # phi from -90° to +90°

        correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)

        for i, n in enumerate(n_values):
            for j, phi in enumerate(phi_values):
                try:
                    # Validate inputs before calculation
                    validation = validate_cpei_peil_inputs(pvel, svel, merged_rhob, n, phi, "PEIL")
                    if not validation['valid']:
                        logger.warning(f"PEIL validation failed for merged data n={safe_format_float(n, 1)}, phi={phi}: {'; '.join(validation['errors'])}")
                        correlation_matrix[i, j] = np.nan
                        continue

                    peil = calculate_peil(pvel, svel, merged_rhob, n, phi)

                    # Validate PEIL output
                    if peil is None:
                        logger.warning(f"PEIL calculation returned None for merged data n={safe_format_float(n, 1)}, phi={phi}")
                        correlation_matrix[i, j] = np.nan
                        continue

                    if hasattr(peil, 'size') and peil.size == 0:
                        logger.warning(f"PEIL calculation returned empty array for merged data n={safe_format_float(n, 1)}, phi={phi}")
                        correlation_matrix[i, j] = np.nan
                        continue

                    correlation = nanaware_corrcoef(peil, merged_target)
                    correlation_matrix[i, j] = correlation

                except Exception as e:
                    error_msg = f"Error calculating PEIL for merged data n={safe_format_float(n, 1)}, phi={phi}: {str(e)}"
                    logger.error(error_msg)
                    print(f"Warning: {error_msg}")
                    correlation_matrix[i, j] = np.nan

        # Find optimal parameters
        if np.all(np.isnan(correlation_matrix)):
            print("All correlations are NaN. Unable to find optimum parameters for merged PEIL.")
            optimal_n_merged = 1.0
            optimal_phi_merged = 0
            max_correlation_merged = np.nan
        else:
            max_idx = np.unravel_index(np.nanargmax(correlation_matrix), correlation_matrix.shape)
            optimal_n_merged = n_values[max_idx[0]]
            optimal_phi_merged = phi_values[max_idx[1]]
            max_correlation_merged = correlation_matrix[max_idx]

        print(f"PEIL optimization complete for merged wells:")
        # Safely format merged PEIL parameters with None value checks
        # Safely format merged PEIL parameters using helper functions
        print(f"  Optimal n: {safe_format_float(optimal_n_merged, precision=1, default='N/A')}")

        optimal_phi_merged_val_peil = safe_format_float(optimal_phi_merged, precision=0, default="N/A")
        phi_display_peil = f"{optimal_phi_merged_val_peil}°" if optimal_phi_merged_val_peil != "N/A" else "N/A"
        print(f"  Optimal phi: {phi_display_peil}")

        print(f"  Maximum correlation coefficient with {target_log_generic_name}: {safe_format_float(max_correlation_merged, precision=4, default='N/A')}")

        # Plot PEIL correlation heatmap for merged wells
        plt.figure(figsize=(12, 8))
        plt.imshow(correlation_matrix, aspect='auto', origin='lower', cmap='viridis')
        plt.colorbar(label='Correlation Coefficient')
        plt.xlabel('Phi (degrees)')
        plt.ylabel('n (exponent)')
        plt.title(f'PEIL-{target_log_generic_name} Correlation Matrix for Merged Wells')

        # Set tick labels
        phi_ticks = range(0, len(phi_values), 30)  # Every 30 degrees
        n_ticks = range(0, len(n_values), 5)  # Every 0.5 in n
        plt.xticks(phi_ticks, [phi_values[i] for i in phi_ticks])
        plt.yticks(n_ticks, [safe_format_float(n_values[i], precision=1, default='N/A') for i in n_ticks])

        # Mark optimal point
        optimal_phi_idx = list(phi_values).index(optimal_phi_merged)
        optimal_n_idx = list(n_values).index(optimal_n_merged)
        # Safely format the plot label with None value checks
        # Safely format the plot label using helper function
        label_text = f'Optimal: {safe_format_parameter_string(optimal_n_merged, optimal_phi_merged, default="n=N/A, phi=N/A")}'
        plt.plot(optimal_phi_idx, optimal_n_idx, 'r*', markersize=15, label=label_text)

        # Add annotation with maximum correlation value
        max_corr_text = safe_format_float(max_correlation_merged, precision=4, default='N/A')
        annotation_text = f'Max Correlation: {max_corr_text}'
        plt.annotate(annotation_text,
                    xy=(optimal_phi_idx, optimal_n_idx),
                    xytext=(optimal_phi_idx + 10, optimal_n_idx + 2),
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.2'),
                    fontsize=10, fontweight='bold')

        # Add summary text box in the corner
        summary_text = f'PEIL Merged Analysis Summary:\n'
        summary_text += f'Optimal n: {safe_format_float(optimal_n_merged, precision=1, default="N/A")}\n'
        summary_text += f'Optimal φ: {safe_format_float(optimal_phi_merged, precision=0, default="N/A")}°\n'
        summary_text += f'Max Correlation: {max_corr_text}\n'
        summary_text += f'Target: {target_log_generic_name}'

        plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8),
                verticalalignment='top', fontsize=9, fontfamily='monospace')

        plt.legend()
        plt.tight_layout()
        plt.show()

        # Store parameters as string for compatibility with safe formatting
        optimum_angle_merged = safe_format_parameter_string(optimal_n_merged, optimal_phi_merged, default=f"n={str(optimal_n_merged)}, phi={str(optimal_phi_merged)}")

    # Calculate analysis results using the (merged) optimum parameters for each well
    all_wells_data_output = []
    for las in las_files: # Iterate through original las_files to generate output for each
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges[well_name]

        current_well_cols = find_default_columns(las, log_keywords_for_finding_cols)

        base_mnemonics = {}
        missing_calc_logs = []
        for generic_name in required_base_generic_logs:
            mnemonic = current_well_cols.get(generic_name)
            if mnemonic is None or mnemonic not in las.curves:
                missing_calc_logs.append(f"{generic_name} (searched: {log_keywords_for_finding_cols.get(generic_name, [])})")
            else:
                base_mnemonics[generic_name] = mnemonic

        target_actual_mnemonic = current_well_cols.get(target_log_generic_name)
        if target_actual_mnemonic is None and target_log_generic_name in las.curves:
            target_actual_mnemonic = target_log_generic_name
            analysis_type_name = "EEI" if analysis_method == 1 else ("CPEI" if analysis_method == 2 else "PEIL")
            print(f"Info: Target log '{target_log_generic_name}' used directly for {analysis_type_name} calc in well {well_name}.")

        if target_actual_mnemonic is None or target_actual_mnemonic not in las.curves:
            missing_calc_logs.append(f"{target_log_generic_name} (resolved mnemonic '{target_actual_mnemonic or 'None'}', searched: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])})")

        vcl_actual_mnemonic = current_well_cols.get('VCL') # Optional

        if missing_calc_logs:
            analysis_type_name = "EEI" if analysis_method == 1 else ("CPEI" if analysis_method == 2 else "PEIL")
            print(f"Warning: For merged {analysis_type_name} calculation plotting, well {well_name} is missing logs: {', '.join(missing_calc_logs)}. Appending placeholder data for this well.")
            all_wells_data_output.append({
                'well_name': well_name, 'depth': None, 'target': None,
                'normalized_eei': None, 'angle': optimum_angle_merged, 'vol_wetclay': None
            })
            continue

        if analysis_method == 1:  # EEI Analysis
            depth_res, target_res, norm_eei_res, vcl_res = calculate_eei(
                las,
                base_mnemonics,
                target_actual_mnemonic,
                vcl_actual_mnemonic,
                top_depth, bottom_depth,
                optimum_angle_merged, # Use the optimum_angle derived from merged data
                calcmethod,
                k_method,
                k_value
            )

            all_wells_data_output.append({
                'well_name': well_name,
                'depth': depth_res,
                'target': target_res,
                'normalized_eei': norm_eei_res,
                'angle': optimum_angle_merged, # Store the angle used
                'vol_wetclay': vcl_res
            })

        elif analysis_method == 2:  # CPEI Analysis
            # Calculate CPEI using optimal parameters for plotting
            depth_res, target_res, norm_cpei_res, vcl_res = calculate_cpei_for_plotting(
                las,
                base_mnemonics,
                target_actual_mnemonic,
                vcl_actual_mnemonic,
                top_depth, bottom_depth,
                optimal_n_merged, optimal_phi_merged
            )

            all_wells_data_output.append({
                'well_name': well_name,
                'depth': depth_res,
                'target': target_res,
                'normalized_eei': norm_cpei_res,  # Use normalized_eei field for compatibility
                'angle': optimum_angle_merged,  # Store the parameters used
                'vol_wetclay': vcl_res
            })

        elif analysis_method == 3:  # PEIL Analysis
            # Calculate PEIL using optimal parameters for plotting
            depth_res, target_res, norm_peil_res, vcl_res = calculate_peil_for_plotting(
                las,
                base_mnemonics,
                target_actual_mnemonic,
                vcl_actual_mnemonic,
                top_depth, bottom_depth,
                optimal_n_merged, optimal_phi_merged
            )

            all_wells_data_output.append({
                'well_name': well_name,
                'depth': depth_res,
                'target': target_res,
                'normalized_eei': norm_peil_res,  # Use normalized_eei field for compatibility
                'angle': optimum_angle_merged,  # Store the parameters used
                'vol_wetclay': vcl_res
            })

    return all_wells_data_output

# show_next_action_dialog function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def show_next_action_dialog():
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.show_next_action_dialog()

def load_boundaries_from_excel(title="Select Excel file with boundary information"):
    """
    Load boundary information from an Excel file.

    Args:
        title: The title to display in the file dialog

    Returns:
        DataFrame containing well names, surface names, and measured depths
        or None if no file was selected or an error occurred.
    """
    root = tk.Tk()
    root.withdraw()

    file_path = filedialog.askopenfilename(
        title=title,
        filetypes=[("Excel files", "*.xls;*.xlsx")]
    )

    if not file_path:
        print("No Excel file selected.")
        return None

    try:
        # Load the Excel file
        df = pd.read_excel(file_path)

        # Check if the required columns exist
        required_columns = ['Well', 'Surface', 'MD']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            messagebox.showerror(
                "Missing Columns",
                f"The Excel file is missing the following required columns: {', '.join(missing_columns)}.\n"
                f"Please ensure the file contains columns named: {', '.join(required_columns)}"
            )
            return None

        # Basic validation
        if df.empty:
            messagebox.showerror("Empty File", "The Excel file contains no data.")
            return None

        print(f"Successfully loaded boundary data from {file_path}")
        print(f"Found {len(df)} boundary entries for {df['Well'].nunique()} wells")
        return df

    except Exception as e:
        messagebox.showerror("Error Loading File", f"An error occurred while loading the Excel file:\n{str(e)}")
        print(f"Error loading Excel file: {str(e)}")
        return None

def filter_excel_data_for_las_wells(df, las_files):
    """
    Filter Excel data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        df: DataFrame containing well names, surface names, and measured depths
        las_files: List of LAS file objects

    Returns:
        Filtered DataFrame containing only boundaries for wells in the LAS files
    """
    if df is None:
        return None

    # Get the list of well names from the LAS files
    las_well_names = [las.well.WELL.value for las in las_files]

    # Filter the DataFrame to only include rows where the Well column value is in las_well_names
    filtered_df = df[df['Well'].isin(las_well_names)]

    # Check if we have any matching wells
    if filtered_df.empty:
        print("Warning: No matching wells found in Excel file. The Excel file contains wells:",
              ", ".join(df['Well'].unique()))
        print("But the loaded LAS files contain wells:", ", ".join(las_well_names))
        return None

    # Log the filtering results
    original_well_count = df['Well'].nunique()
    filtered_well_count = filtered_df['Well'].nunique()
    print(f"Filtered Excel data from {original_well_count} wells to {filtered_well_count} wells that match loaded LAS files")
    print(f"Retained wells: {', '.join(filtered_df['Well'].unique())}")

    return filtered_df

def load_excel_depth_ranges(las_files):
    """
    Prompt the user to load an Excel file with depth ranges at the beginning of the program.
    Filter the data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        las_files: List of LAS file objects

    Returns:
        DataFrame containing well names, surface names, and measured depths (filtered for LAS wells)
        or None if no file was selected, the user canceled, or an error occurred.
    """
    # Ask user if they want to load an Excel file with depth ranges
    root = tk.Tk()
    root.withdraw()

    load_excel = messagebox.askyesno(
        "Load Depth Ranges Excel",
        "Would you like to load an Excel file containing depth ranges now?\n\n"
        "The file should have columns named 'Well', 'Surface', and 'MD'."
    )

    if not load_excel:
        print("User chose not to load Excel file with depth ranges at this time.")
        return None

    # Load the Excel file
    df = load_boundaries_from_excel("Select Excel file with depth ranges")

    # Filter the data to only include boundaries for wells in the LAS files
    return filter_excel_data_for_las_wells(df, las_files)

# select_boundaries_for_all_wells function moved to ui/dialog_systems.py as a helper method

# Legacy function wrapper for backward compatibility
def select_boundaries_for_all_wells(df, las_well_names):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems._select_boundaries_for_all_wells(df, las_well_names)

def select_boundaries_from_excel(df, well_name):
    """
    Create a dialog to select top and bottom boundaries from Excel data for a specific well.
    The dialog will only show boundaries for the specified well.

    Args:
        df: DataFrame containing boundary data
        well_name: Name of the well to filter data for

    Returns:
        Tuple of (top_depth, bottom_depth) or None if cancelled
    """
    # Filter data for the current well
    well_data = df[df['Well'] == well_name]

    if well_data.empty:
        messagebox.showerror(
            "Missing Well Data",
            f"No boundary data found for well '{well_name}' in the Excel file."
        )
        return None

    # Sort the data by depth to make selection more intuitive
    well_data = well_data.sort_values('MD')

    # Create dialog
    dialog = tk.Toplevel()
    dialog.title(f"Select Boundaries for {well_name}")
    dialog.geometry("400x300")

    # Create frame
    frame = ttk.Frame(dialog, padding="10")
    frame.pack(fill=tk.BOTH, expand=True)

    # Get unique surface names for this well
    surfaces = well_data['Surface'].unique().tolist()

    # Create variables to store selections
    top_surface_var = tk.StringVar()
    bottom_surface_var = tk.StringVar()

    # Set default values if available
    if len(surfaces) > 0:
        top_surface_var.set(surfaces[0])
    if len(surfaces) > 1:
        bottom_surface_var.set(surfaces[-1])

    # Function to update depth labels when surface selection changes
    def update_depth_labels(*args):
        top_surface = top_surface_var.get()
        bottom_surface = bottom_surface_var.get()

        top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
        bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

        if len(top_md) > 0:
            top_depth_label.config(text=f"Depth: {top_md[0]:.2f}")
        else:
            top_depth_label.config(text="Depth: N/A")

        if len(bottom_md) > 0:
            bottom_depth_label.config(text=f"Depth: {bottom_md[0]:.2f}")
        else:
            bottom_depth_label.config(text="Depth: N/A")

    # Create widgets
    ttk.Label(frame, text="Select Top Boundary:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
    top_combo = ttk.Combobox(frame, textvariable=top_surface_var, values=surfaces, state="readonly")
    top_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
    top_depth_label = ttk.Label(frame, text="Depth: ")
    top_depth_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

    ttk.Label(frame, text="Select Bottom Boundary:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
    bottom_combo = ttk.Combobox(frame, textvariable=bottom_surface_var, values=surfaces, state="readonly")
    bottom_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
    bottom_depth_label = ttk.Label(frame, text="Depth: ")
    bottom_depth_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

    # Register callbacks
    top_surface_var.trace_add("write", update_depth_labels)
    bottom_surface_var.trace_add("write", update_depth_labels)

    # Initialize depth labels
    update_depth_labels()

    # Result variable
    result = {"top_depth": None, "bottom_depth": None, "cancelled": False}

    def on_ok():
        # Show processing indicator
        status_label = ttk.Label(frame, text="Processing...", font=("", 9, "italic"))
        status_label.grid(row=5, column=0, columnspan=2, pady=(5, 0))
        dialog.update_idletasks()  # Force UI update

        top_surface = top_surface_var.get()
        bottom_surface = bottom_surface_var.get()

        top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
        bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

        if len(top_md) == 0 or len(bottom_md) == 0:
            status_label.destroy()
            messagebox.showerror("Missing Data", "Could not retrieve depth values for selected surfaces.")
            return

        result["top_depth"] = float(top_md[0])
        result["bottom_depth"] = float(bottom_md[0])

        # Update status and close dialog
        status_label.config(text="Selection complete! Closing...")
        dialog.update_idletasks()  # Force UI update

        # Use after() to ensure the UI updates before destroying the dialog
        dialog.after(300, lambda: dialog.destroy())

    def on_cancel():
        result["cancelled"] = True
        dialog.destroy()

    # Create buttons
    button_frame = ttk.Frame(frame)
    button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

    ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)

    # Configure grid weights
    frame.columnconfigure(1, weight=1)

    # Run dialog
    dialog.protocol("WM_DELETE_WINDOW", on_cancel)
    dialog.transient()
    dialog.grab_set()

    # Make the dialog modal and wait for it to be destroyed
    dialog.wait_visibility()
    dialog.focus_set()
    dialog.wait_window()

    if result["cancelled"]:
        print(f"Selection cancelled for well {well_name}.")
        return None

    print(f"Selection completed for well {well_name}: ({result['top_depth']:.2f}, {result['bottom_depth']:.2f})")
    return (result["top_depth"], result["bottom_depth"])

# get_depth_ranges function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def get_depth_ranges(las_files, log_keywords_for_finding_cols, preloaded_excel_df=None):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_depth_ranges(las_files, log_keywords_for_finding_cols, preloaded_excel_df)

# get_target_log function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def get_target_log(available_logs, common_actual_mnemonics, common_generic_keywords, calculator_used=False):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_target_log(available_logs, common_actual_mnemonics, common_generic_keywords, calculator_used)


# select_alternative_mnemonic function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def select_alternative_mnemonic(las, missing_target_log, well_name):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.select_alternative_mnemonic(las, missing_target_log, well_name)


def run_eei_analysis():
    """
    Run the complete EEI analysis workflow.

    Returns:
        bool: True if user wants to restart, False if user wants to exit
    """
    # Load LAS files
    las_files = load_multiple_las_files()
    if not las_files:
        print("No LAS files selected. Exiting.")
        return False

    # Validate essential logs
    validation_results = validate_essential_logs(las_files, log_keywords)
    validation_summary = generate_validation_report(validation_results)

    # Categorize log curves
    log_categories = categorize_log_curves(las_files)
    display_log_inventory(log_categories)

    # Note: Log availability will be calculated after derived log calculations


    # Dictionary to track alternative mnemonics selected by the user
    alternative_mnemonics = {}

    # Ask user if they want to continue despite missing logs
    if validation_summary['invalid_files'] > 0:
        root = tk.Tk()
        root.withdraw()
        continue_analysis = messagebox.askyesno(
            "Missing Essential Logs",
            f"{validation_summary['invalid_files']} out of {validation_summary['total_files']} files are missing essential logs. Continue anyway?"
        )
        if not continue_analysis:
            print("Analysis cancelled by user. Exiting.")
            return False

    # Calculate additional logs and add them to the LAS files
    for las in las_files:
        log_available_curves(las)  # Log the available curves for each well
        # Use find_default_columns to get specific mnemonics for this well
        current_well_mnemonics = find_default_columns(las, log_keywords)

        # Check for PHIE, SWE, PHIT, SWT for derived logs
        phie_m = current_well_mnemonics.get('PHIE')
        swe_m = current_well_mnemonics.get('SWE')
        phit_m = current_well_mnemonics.get('PHIT')
        swt_m = current_well_mnemonics.get('SWT')

        if all(m and m in las.curves for m in [phie_m, swe_m, phit_m, swt_m]):
            phie_data = np.array(las[phie_m].data)
            swe_data = np.array(las[swe_m].data)
            phit_data = np.array(las[phit_m].data)
            swt_data = np.array(las[swt_m].data)

            las.append_curve('PHIE_1_SWE', phie_data * (1 - swe_data), unit='v/v', descr='PHIE * (1-SWE)')
            las.append_curve('PHIT_1_SWT', phit_data * (1 - swt_data), unit='v/v', descr='PHIT * (1-SWT)')
        else:
            print(f"Info: Skipping PHIE_1_SWE/PHIT_1_SWT calculation for well {las.well.WELL.value} due to missing one or more input logs (PHIE, SWE, PHIT, SWT).")

        # Check for NPHI, SWT for NPHI_SHC
        nphi_m = current_well_mnemonics.get('NPHI')
        # swt_m is already defined from above
        if nphi_m and nphi_m in las.curves and swt_m and swt_m in las.curves: # Ensure swt_m is valid here too
            nphi_data = np.array(las[nphi_m].data)
            swt_data_for_nphi_shc = np.array(las[swt_m].data) # Re-access swt_data for clarity or if scope changes
            nphi_shc_data = nphi_data * (1 - swt_data_for_nphi_shc)
            las.append_curve('NPHI_SHC', nphi_shc_data, unit='v/v', descr='NPHI * (1-SWT)')
        else:
            print(f"Info: Skipping NPHI_SHC calculation for well {las.well.WELL.value} due to missing NPHI or SWT.")

        # Check for KDRY, KSOLID
        kdry_m = current_well_mnemonics.get('KDRY')
        ksolid_m = current_well_mnemonics.get('KSOLID')
        if kdry_m and kdry_m in las.curves and ksolid_m and ksolid_m in las.curves:
            kdry_data = np.array(las[kdry_m].data)
            ksolid_data = np.array(las[ksolid_m].data)
            kdks_data = np.where(ksolid_data != 0, kdry_data / ksolid_data, np.nan)
            las.append_curve('KDKS', kdks_data, unit='', descr='KDRY / KSOLID')
        else:
            print(f"Info: Skipping KDKS calculation for well {las.well.WELL.value} due to missing KDRY or KSOLID.")

        # Check for GDRY, GSOLID
        gdry_m = current_well_mnemonics.get('GDRY')
        gsolid_m = current_well_mnemonics.get('GSOLID')
        if gdry_m and gdry_m in las.curves and gsolid_m and gsolid_m in las.curves:
            gdry_data = np.array(las[gdry_m].data)
            gsolid_data = np.array(las[gsolid_m].data)
            gdgs_data = np.where(gsolid_data != 0, gdry_data / gsolid_data, np.nan)
            las.append_curve('GDGS', gdgs_data, unit='', descr='GDRY / GSOLID')
        else:
            print(f"Info: Skipping GDGS calculation for well {las.well.WELL.value} due to missing GDRY or GSOLID.")

    # UPDATE LOG AVAILABILITY AFTER DERIVED LOG CALCULATIONS
    # Recalculate which logs are present in all wells (including derived logs like PHIE_1_SWE, etc.)
    print("Updating log availability after derived log calculations...")
    log_categories = categorize_log_curves(las_files)

    # Recalculate common logs after derived log additions
    total_wells = len(las_files)
    common_actual_mnemonics = set()
    common_generic_keywords = set()

    for curve_key, info in log_categories.items():
        if len(info['files']) == total_wells:
            # This curve (by its uppercase mnemonic) is now in all files
            common_actual_mnemonics.add(curve_key)

            # Check if this common mnemonic is an alias for any generic keyword
            for generic_keyword, aliases in log_keywords.items():
                if curve_key in [alias.upper() for alias in aliases]:
                    common_generic_keywords.add(generic_keyword)

    print(f"After derived logs: {len(common_actual_mnemonics)} actual mnemonics present in all {total_wells} wells.")
    print(f"After derived logs: {len(common_generic_keywords)} generic keywords available in all {total_wells} wells.")

    # ENHANCED CALCULATOR INTEGRATION FOR EEI ANALYSIS
    # Following guidelines from calculator_Addition_EEI.md
    print("Checking if user wants to create custom calculated logs...")

    root = tk.Tk()
    root.withdraw()
    use_calculator = messagebox.askyesno(
        "Custom Log Calculator",
        "Would you like to create custom calculated logs before selecting the target log?\n\n"
        "This allows you to create new logs using mathematical operations on existing curves.\n"
        "Examples: porosity combinations, elastic ratios, normalized logs, etc.\n\n"
        "Note: Only logs available in ALL wells can be used safely."
    )

    calculator_used = False  # Track if calculator was used for target log selection

    if use_calculator:
        print("Opening enhanced calculator for custom log creation...")

        # Show log availability summary first
        log_analysis = analyze_log_availability(las_files)
        summary_message = (
            f"Log Availability Summary:\n\n"
            f"✅ Available in all {log_analysis['total_wells']} wells: {len(log_analysis['common_logs'])} logs\n"
            f"⚠️ Available in some wells: {len(log_analysis['partial_logs'])} logs\n\n"
            f"For safe calculations, use only logs marked with ✅ in the calculator.\n"
            f"Using ⚠️ logs may cause errors in wells where they're missing."
        )

        messagebox.showinfo("Log Availability Summary", summary_message)

        # Run enhanced calculator with error handling
        calculator_success = get_calculations_for_eei(las_files)

        if calculator_success:
            calculator_used = True  # Mark that calculator was successfully used
            print("✅ Custom logs created successfully.")

            # Update log categories to include new logs
            log_categories = categorize_log_curves(las_files)
            print("Updated log inventory with new calculated logs.")

            # UPDATE COMMON LOG AVAILABILITY AFTER CALCULATOR
            # Recalculate which logs are now present in all wells (including new calculated logs)
            print("Updating log availability for target selection...")

            # Recalculate common logs after calculator additions
            total_wells = len(las_files)
            common_actual_mnemonics = set()
            common_generic_keywords = set()

            for curve_key, info in log_categories.items():
                if len(info['files']) == total_wells:
                    # This curve (by its uppercase mnemonic) is now in all files
                    common_actual_mnemonics.add(curve_key)

                    # Check if this common mnemonic is an alias for any generic keyword
                    for generic_keyword, aliases in log_keywords.items():
                        if curve_key in [alias.upper() for alias in aliases]:
                            common_generic_keywords.add(generic_keyword)

            print(f"Updated: {len(common_actual_mnemonics)} actual mnemonics now present in all {total_wells} wells.")
            print(f"Updated: {len(common_generic_keywords)} generic keywords now available in all {total_wells} wells.")

            # Optionally show updated inventory
            show_updated = messagebox.askyesno(
                "Calculator Complete",
                "Custom logs have been created successfully!\n\n"
                f"Log availability updated:\n"
                f"• {len(common_actual_mnemonics)} logs now available in all wells\n"
                f"• New calculated logs will appear with ★🧮 in target selection\n"
                f"• Both derived logs (PHIE_1_SWE, etc.) and custom logs are now available\n\n"
                "Would you like to see the updated log inventory?"
            )

            if show_updated:
                display_log_inventory(log_categories)
        else:
            print("ℹ️ Calculator operation cancelled or failed. Continuing with existing logs.")

    # Continue with existing target log selection
    print("Proceeding to target log selection...")

    # Get user input for the target log (now refers to the generic name)
    # Note: common_actual_mnemonics and common_generic_keywords are now updated if calculator was used
    all_available_mnemonics = set()
    for las_item in las_files:
        all_available_mnemonics.update(las_item.curves.keys())

    generic_target_options = sorted(list(set(log_keywords.keys()) | all_available_mnemonics))

    # Pass calculator_used flag to enhance target log selection display
    selected_target_log_generic_name = get_target_log(generic_target_options, common_actual_mnemonics, common_generic_keywords, calculator_used)

    if not selected_target_log_generic_name:
        print("No target log selected. Exiting.")
        return False

    found_in_at_least_one_well = False
    for las_item in las_files:
        temp_cols = find_default_columns(las_item, log_keywords)
        # Check if selected name is a direct mnemonic OR if it's a generic keyword that maps to an existing mnemonic
        if selected_target_log_generic_name in las_item.curves or \
           (selected_target_log_generic_name in temp_cols and temp_cols[selected_target_log_generic_name] in las_item.curves):
            found_in_at_least_one_well = True
            break
    if not found_in_at_least_one_well:
        print(f"Warning: Selected target log '{selected_target_log_generic_name}' does not appear to be a valid log in any of the loaded LAS files, either directly or via keyword mapping. Analysis might yield no results.")
        # Consider exiting or asking user to re-select
        # For now, proceed, but functions should handle missing target logs gracefully.

    # Prompt user to load Excel file with depth ranges upfront
    # Pass the LAS files to filter the Excel data to only include matching wells
    preloaded_excel_df = load_excel_depth_ranges(las_files)

    # Provide feedback if Excel data was loaded
    if preloaded_excel_df is not None:
        print(f"Excel file with depth ranges loaded successfully.")
        print(f"Found {len(preloaded_excel_df)} boundary entries for {preloaded_excel_df['Well'].nunique()} wells.")
        print("This data will be automatically used in the depth ranges dialog.")

    # Get depth ranges, passing the preloaded Excel data if available
    depth_ranges = get_depth_ranges(las_files, log_keywords, preloaded_excel_df)


    # Get analysis type and associated parameters from user
    analysis_method, calcmethod, k_method, k_value = get_analysis_type_and_parameters()
    if analysis_method is None:
        print("No analysis type selected. Exiting.")
        return False

    analysis_type = simpledialog.askstring("Analysis Type", "Enter '1' for individual well analysis or '2' for merged analysis:")
    if analysis_type is None:
        print("No analysis type selected. Exiting.")
        return False

    all_wells_data_for_plotting = []

    if analysis_type == '1':
        all_wells_results, all_wells_data_for_plotting = individual_well_analysis(
            las_files, log_keywords, selected_target_log_generic_name, depth_ranges, analysis_method, calcmethod, k_method, k_value, alternative_mnemonics
        )

        valid_results_for_summary = [res for res in all_wells_results if res.get('optimum_angle') is not None and res.get('max_correlation') is not None]

        if not valid_results_for_summary:
            print("No valid results from individual well analysis to plot summary chart.")
        else:
            fig, ax1 = plt.subplots(figsize=(14, 8))
            well_names_summary = [result['well_name'] for result in valid_results_for_summary]
            optimum_angles_summary = [result['optimum_angle'] for result in valid_results_for_summary]
            max_correlations_summary = [result['max_correlation'] for result in valid_results_for_summary]

            ax1.set_xlabel('Well Name')

            # Handle different analysis types for plotting
            if analysis_method == 1:  # EEI - optimum_angle is numeric
                ax1.set_ylabel('Optimum Angle (degrees)', color='tab:blue')
                ax1.bar(well_names_summary, optimum_angles_summary, color='tab:blue', alpha=0.7)
            else:  # CPEI/PEIL - optimum_angle is string, use correlation values for bar height
                ax1.set_ylabel('Max Correlation Coefficient', color='tab:blue')
                ax1.bar(well_names_summary, max_correlations_summary, color='tab:blue', alpha=0.7)

            ax1.tick_params(axis='y', labelcolor='tab:blue')

            # Handle second axis based on analysis type
            if analysis_method == 1:  # EEI - show correlation on second axis
                ax2 = ax1.twinx()
                ax2.set_ylabel('Max Correlation Coefficient', color='tab:orange')
                ax2.plot(well_names_summary, max_correlations_summary, color='tab:orange', marker='o')
                ax2.tick_params(axis='y', labelcolor='tab:orange')

            for i, result in enumerate(valid_results_for_summary):
                # Handle different analysis types - EEI uses numeric angles, CPEI/PEIL use string descriptions
                if analysis_method == 1:  # EEI - optimum_angle is numeric
                    angle_text = f"{safe_format_float(result['optimum_angle'], precision=1, default='N/A')}°"
                    y_position = result['optimum_angle']
                else:  # CPEI/PEIL - optimum_angle is a string description
                    angle_text = result['optimum_angle']
                    y_position = result['max_correlation']  # Use correlation value for y position

                ax1.text(i, y_position,
                         f"{angle_text}\n{safe_format_float(result['max_correlation'], precision=3, default='N/A')}\n{safe_format_float(result['top_depth'], precision=0, default='N/A')}-{safe_format_float(result['bottom_depth'], precision=0, default='N/A')}",
                         ha='center', va='bottom', fontweight='bold', fontsize=8)

            # Set title based on analysis method
            if analysis_method == 1:
                analysis_name = "EEI"
            elif analysis_method == 2:
                analysis_name = "CPEI"
            elif analysis_method == 3:
                analysis_name = "PEIL"
            else:
                analysis_name = "Unknown"

            plt.title(f'Optimum Parameters and Max Correlation for {analysis_name}-{selected_target_log_generic_name}\nDepth ranges vary by well')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.show()

    elif analysis_type == '2':
        all_wells_data_for_plotting = merged_well_analysis(
            las_files, log_keywords, selected_target_log_generic_name, depth_ranges, analysis_method, calcmethod, k_method, k_value, alternative_mnemonics
        )

    else:
        print("Invalid analysis type selected. Exiting.")
        return False

    final_plottable_wells_data = [
        data for data in all_wells_data_for_plotting
        if data.get('depth') is not None and
           data.get('target') is not None and
           data.get('normalized_eei') is not None
    ]

    if not final_plottable_wells_data:
        print("No plottable data available after analysis. Check logs and depth ranges.")
    else:
        plot_eei_vs_target(final_plottable_wells_data, selected_target_log_generic_name, depth_ranges)

    # Print summary of alternative mnemonics used
    if alternative_mnemonics:
        print("\n" + "="*80)
        print("ALTERNATIVE MNEMONICS SUMMARY")
        print("="*80)
        print("{:<20} {:<20} {:<20} {:<20}".format("Well Name", "Target Log", "Alternative Used", "Status"))
        print("-"*80)

        for well_key, alternative in alternative_mnemonics.items():
            well_name, target_name = well_key.split(":")
            status = "USED" if alternative else "SKIPPED"
            alternative_display = alternative if alternative else "N/A"
            print("{:<20} {:<20} {:<20} {:<20}".format(well_name, target_name, alternative_display, status))

        print("="*80)

    print("Analysis complete.")

    # Show dialog asking user what to do next
    user_choice = show_next_action_dialog()

    if user_choice == 'restart':
        print("\n" + "="*80)
        print("🔄 STARTING NEW ANALYSIS")
        print("="*80)
        print("Restarting the analysis workflow...\n")
        return True  # Signal to restart
    else:
        print("\n" + "="*80)
        print("🚪 EXITING PROGRAM")
        print("="*80)
        print("Thank you for using the EEI Analysis tool!")
        return False  # Signal to exit


# MAIN EXECUTION SCRIPT
if __name__ == "__main__":
    print("="*80)
    print("🎯 EEI ANALYSIS TOOL")
    print("="*80)
    print("Welcome to the Extended Elastic Impedance (EEI) Analysis Tool!")
    print("This tool performs correlation analysis between EEI and target logs.\n")

    # Main execution loop
    while True:
        try:
            # Run the analysis
            should_restart = run_eei_analysis()

            # Check if user wants to restart or exit
            if not should_restart:
                break

        except KeyboardInterrupt:
            print("\n\nProgram interrupted by user. Exiting...")
            break
        except Exception as e:
            print(f"\n❌ An unexpected error occurred: {str(e)}")
            print("The program will exit. Please check your data and try again.")
            break

    print("\nProgram terminated.")
    print("="*80)